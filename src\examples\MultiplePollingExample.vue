<template>
  <div class="multiple-polling-example">
    <h2>多轮询示例</h2>

    <!-- 用户数据轮询 -->
    <el-card style="margin-bottom: 20px">
      <template #header>
        <div class="card-header">
          <span>用户数据轮询</span>
          <el-button
            :type="userPolling.isRunning ? 'danger' : 'primary'"
            @click="toggleUserPolling"
          >
            {{ userPolling.isRunning ? '停止' : '开始' }}
          </el-button>
        </div>
      </template>

      <div class="polling-info">
        <p>状态: {{ userPolling.loading.value ? '加载中' : '空闲' }}</p>
        <p>数据条数: {{ userPolling.data.value.length }}</p>
        <p>总数: {{ userPolling.total.value }}</p>
        <p>错误: {{ userPolling.error.value?.message || '无' }}</p>
      </div>
    </el-card>

    <!-- 港口类型数据轮询 -->
    <el-card style="margin-bottom: 20px">
      <template #header>
        <div class="card-header">
          <span>港口类型数据轮询</span>
          <el-button
            :type="portTypePolling.isRunning ? 'danger' : 'primary'"
            @click="togglePortTypePolling"
          >
            {{ portTypePolling.isRunning ? '停止' : '开始' }}
          </el-button>
        </div>
      </template>

      <div class="polling-info">
        <p>状态: {{ portTypePolling.loading.value ? '加载中' : '空闲' }}</p>
        <p>数据条数: {{ portTypePolling.data.value.length }}</p>
        <p>总数: {{ portTypePolling.total.value }}</p>
        <p>错误: {{ portTypePolling.error.value?.message || '无' }}</p>
      </div>
    </el-card>

    <!-- 自定义API轮询 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>自定义API轮询</span>
          <el-button
            :type="customPolling.isRunning ? 'danger' : 'primary'"
            @click="toggleCustomPolling"
          >
            {{ customPolling.isRunning ? '停止' : '开始' }}
          </el-button>
        </div>
      </template>

      <div class="polling-info">
        <p>状态: {{ customPolling.loading.value ? '加载中' : '空闲' }}</p>
        <p>数据条数: {{ customPolling.data.value.length }}</p>
        <p>总数: {{ customPolling.total.value }}</p>
        <p>错误: {{ customPolling.error.value?.message || '无' }}</p>
      </div>
    </el-card>

    <!-- 全局控制 -->
    <div class="global-controls" style="margin-top: 20px">
      <el-button @click="startAllPolling">启动所有轮询</el-button>
      <el-button @click="stopAllPolling">停止所有轮询</el-button>
      <el-button @click="refreshAllPolling">刷新所有数据</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onUnmounted } from 'vue'
import { UserDataPolling } from '@/utils/UserDataPolling'
import { getRolePage } from '@/api/system/role'
import { DataPollingBase, type DataPollingConfig } from '@/utils/DataPollingBase'

/** 多轮询示例组件 */
defineOptions({ name: 'MultiplePollingExample' })

// ========== 1. 用户数据轮询 ==========
const userPolling = new UserDataPolling(
  { pageNo: 1, pageSize: 5 },
  { interval: 2000, autoStart: true }
)

// ========== 2. 角色类型数据轮询，继承基类调用API接口即可 ==========
class PortTypePolling extends DataPollingBase<any, any> {
  protected async fetchData(params: any): Promise<PageResult<any>> {
    // 模拟港口类型API调用
    const result = await getRolePage(params) //这里配置自己封装的api方法

    // 验证返回数据格式
    if (!result || typeof result !== 'object') {
      throw new Error('API返回数据格式不正确')
    }

    // 确保返回的数据有正确的结构
    return {
      list: Array.isArray(result.list) ? result.list : [],
      total: typeof result.total === 'number' ? result.total : 0,
      ...result
    }
  }
  //数据更新的监听函数
  protected onDataUpdated(result: PageResult<any>): void {
    console.log('港口类型数据已更新:', result.total)
  }
  //获取失败的监听函数
  protected onError(error: Error): void {
    console.error('港口类型数据获取失败:', error.message)
  }
}
//创建轮询实例（两组参数：第一组为查询页码和单页大小，第二组为轮询配置）
//   interval?: number // 轮询间隔（毫秒），默认 1000
//   immediate?: boolean // 是否立即执行一次，默认 true
//   autoStart?: boolean // 是否自动开始，默认 true
const portTypePolling = new PortTypePolling(
  { pageNo: 1, pageSize: 10 },
  { interval: 3000, autoStart: true }
)

// ========== 3. 自定义API轮询 ==========
class CustomApiPolling extends DataPollingBase<any, any> {
  protected async fetchData(params: any): Promise<PageResult<any>> {
    // 这里可以调用任何你想要的API
    // 例如：return await getYourCustomApi(params)

    // 模拟自定义API调用
    await new Promise((resolve) => setTimeout(resolve, 800))
    return {
      list: [
        { id: 1, status: 'online', timestamp: new Date().toISOString() },
        { id: 2, status: 'offline', timestamp: new Date().toISOString() }
      ],
      total: 2
    }
  }

  protected onDataUpdated(result: PageResult<any>): void {
    console.log('自定义数据已更新:', result.total)
  }

  protected onError(error: Error): void {
    console.error('自定义数据获取失败:', error.message)
  }
}

const customPolling = new CustomApiPolling(
  { pageNo: 1, pageSize: 20 },
  { interval: 5000, autoStart: false } // 默认不自动启动
)

// ========== 控制方法 ==========
const toggleUserPolling = () => {
  if (userPolling.isRunning) {
    userPolling.stop()
  } else {
    userPolling.start()
  }
}

const togglePortTypePolling = () => {
  if (portTypePolling.isRunning) {
    portTypePolling.stop()
  } else {
    portTypePolling.start()
  }
}

const toggleCustomPolling = () => {
  if (customPolling.isRunning) {
    customPolling.stop()
  } else {
    customPolling.start()
  }
}

const startAllPolling = () => {
  userPolling.start()
  portTypePolling.start()
  customPolling.start()
}

const stopAllPolling = () => {
  userPolling.stop()
  portTypePolling.stop()
  customPolling.stop()
}

const refreshAllPolling = async () => {
  await Promise.all([userPolling.refresh(), portTypePolling.refresh(), customPolling.refresh()])
}

// 组件卸载时清理所有轮询
onUnmounted(() => {
  userPolling.destroy()
  portTypePolling.destroy()
  customPolling.destroy()
})
</script>

<style scoped>
.multiple-polling-example {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.polling-info p {
  margin: 5px 0;
  font-size: 14px;
}

.global-controls {
  text-align: center;
}

.global-controls .el-button {
  margin: 0 5px;
}
</style>
