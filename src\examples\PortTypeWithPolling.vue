<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="港口类型名称" prop="protTypeName">
        <el-input
          v-model="queryParams.protTypeName"
          placeholder="请输入港口类型名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" />
          重置
        </el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['port:type:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" />
          新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['port:type:export']"
        >
          <Icon icon="ep:download" class="mr-5px" />
          导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 轮询控制面板 -->
  <ContentWrap>
    <el-card>
      <template #header>
        <div class="card-header">
          <span>实时数据轮询控制</span>
        </div>
      </template>
      <div class="polling-controls">
        <div class="control-group">
          <el-button 
            :type="typePolling.isRunning ? 'danger' : 'primary'"
            @click="togglePolling"
          >
            <Icon :icon="typePolling.isRunning ? 'ep:video-pause' : 'ep:video-play'" class="mr-5px" />
            {{ typePolling.isRunning ? '停止轮询' : '开始轮询' }}
          </el-button>
          
          <el-button @click="typePolling.refresh()">
            <Icon icon="ep:refresh" class="mr-5px" />
            手动刷新
          </el-button>
        </div>

        <div class="control-group">
          <span class="label">轮询间隔:</span>
          <el-input-number
            v-model="pollingInterval"
            :min="500"
            :max="30000"
            :step="500"
            @change="updateInterval"
            style="width: 120px;"
          />
          <span class="unit">毫秒</span>
        </div>

        <div class="control-group">
          <el-tag 
            :type="typePolling.isRunning ? 'success' : 'info'"
            size="large"
          >
            <Icon :icon="typePolling.isRunning ? 'ep:loading' : 'ep:circle-close'" class="mr-5px" />
            {{ typePolling.isRunning ? '轮询中' : '已停止' }}
          </el-tag>
          
          <el-tag 
            :type="typePolling.loading ? 'warning' : 'success'"
            size="large"
          >
            {{ typePolling.loading ? '加载中' : '空闲' }}
          </el-tag>
        </div>
      </div>

      <!-- 数据统计 -->
      <div class="stats-row">
        <div class="stat-item">
          <span class="stat-label">总记录数:</span>
          <span class="stat-value">{{ typePolling.total }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">当前页:</span>
          <span class="stat-value">{{ typePolling.queryParams.pageNo }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">每页条数:</span>
          <span class="stat-value">{{ typePolling.queryParams.pageSize }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">最后更新:</span>
          <span class="stat-value">{{ lastUpdateTime }}</span>
        </div>
      </div>
    </el-card>
  </ContentWrap>

  <!-- 错误提示 -->
  <ContentWrap v-if="typePolling.error">
    <el-alert
      :title="`数据获取失败: ${typePolling.error.message}`"
      type="error"
      :closable="false"
      show-icon
    />
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table 
      :data="typePolling.data" 
      :loading="typePolling.loading"
      border
    >
      <el-table-column prop="portTypeSN" label="ID" width="100" />
      <el-table-column prop="protTypeName" label="港口类型名称" />
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.portTypeSN)"
            v-hasPermi="['port:type:update']"
          >
            修改
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.portTypeSN)"
            v-hasPermi="['port:type:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="typePolling.total"
      v-model:page="typePolling.queryParams.pageNo"
      v-model:limit="typePolling.queryParams.pageSize"
      @pagination="handlePagination"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <TypeForm ref="formRef" @success="typePolling.refresh" />
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted } from 'vue'
import download from '@/utils/download'
import { TypeApi, TypeVO } from '@/api/port/type'
import { DataPollingBase, type DataPollingConfig } from '@/utils/DataPollingBase'
import TypeForm from '@/views/port/type/TypeForm.vue'

/** 港口类型轮询列表 */
defineOptions({ name: 'PortTypeWithPolling' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

// 轮询间隔控制
const pollingInterval = ref(2000) // 默认2秒
const lastUpdateTime = ref('')

// 查询参数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  protTypeName: undefined,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/**
 * 港口类型数据轮询类
 */
class PortTypePolling extends DataPollingBase<TypeVO, typeof queryParams> {
  protected async fetchData(params: typeof queryParams): Promise<PageResult<TypeVO>> {
    return await TypeApi.getTypePage(params)
  }

  protected onDataUpdated(result: PageResult<TypeVO>): void {
    lastUpdateTime.value = new Date().toLocaleTimeString()
    console.log(`港口类型数据已更新，共 ${result.total} 条记录`)
  }

  protected onError(error: Error): void {
    console.error('获取港口类型数据失败:', error.message)
    message.error(`数据获取失败: ${error.message}`)
  }
}

// 创建轮询实例
const typePolling = new PortTypePolling(
  queryParams,
  {
    interval: pollingInterval.value,
    immediate: true,
    autoStart: true,
    continueOnError: true
  }
)

/**
 * 切换轮询状态
 */
const togglePolling = () => {
  if (typePolling.isRunning) {
    typePolling.stop()
  } else {
    typePolling.start()
  }
}

/**
 * 更新轮询间隔
 */
const updateInterval = (newInterval: number) => {
  typePolling.setInterval(newInterval)
}

/**
 * 搜索按钮操作
 */
const handleQuery = () => {
  queryParams.pageNo = 1
  typePolling.updateParams(queryParams)
}

/**
 * 重置按钮操作
 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  queryParams.protTypeName = undefined
  handleQuery()
}

/**
 * 分页处理
 */
const handlePagination = () => {
  typePolling.updateParams(queryParams)
}

/**
 * 添加/修改操作
 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/**
 * 删除按钮操作
 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TypeApi.deleteType(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await typePolling.refresh()
  } catch {}
}

/**
 * 导出按钮操作
 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await TypeApi.exportType(queryParams)
    download.excel(data, '港口类型.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

// 组件卸载时清理资源
onUnmounted(() => {
  typePolling.destroy()
})
</script>

<style scoped>
.polling-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
  margin-bottom: 15px;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.label {
  font-weight: 500;
  color: #666;
}

.unit {
  color: #999;
  font-size: 12px;
}

.stats-row {
  display: flex;
  gap: 30px;
  padding: 15px 0;
  border-top: 1px solid #ebeef5;
  margin-top: 15px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.stat-label {
  font-weight: 500;
  color: #666;
  font-size: 14px;
}

.stat-value {
  font-weight: bold;
  color: #333;
  font-size: 14px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
