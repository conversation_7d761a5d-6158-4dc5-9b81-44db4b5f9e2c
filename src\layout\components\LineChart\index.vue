<template>
  <div class="voltage-chart">
    <div class="titles">
      <slot name="title">控制终端概况</slot>
    </div>
    <div ref="chartRef" class="chart"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import * as echarts from "echarts";

const chartRef = ref();
let chartInstance = null;
const props = defineProps({
  chartData: {
    type: Array,
    default: () => [
      {
        name: "AB电压",
        Vdata: [
          25,
          22,
          18,
          15,
          13,
          14,
          16,
          18,
          21,
          24,
          27,
          29,
          27,
          25,
          22,
          19,
          16,
          15,
          17,
          20,
        ],
      },
      {
        name: "AC电压",
        Vdata: [
          10,
          13,
          17,
          19,
          20,
          18,
          16,
          15,
          13,
          12,
          10,
          11,
          13,
          14,
          15,
          16,
          17,
          19,
          21,
          22,
        ],
      },
      {
        name: "BC电压",
        Vdata: [
          15,
          15,
          16,
          16,
          15,
          15,
          16,
          16,
          16,
          15,
          14,
          13,
          13,
          14,
          14,
          15,
          15,
          15,
          15,
          15,
        ],
      },
    ],
  },
});

// 假设这是从后端获取的数据（你也可以用 axios 获取）
const serverData = ref(props.chartData);

// 颜色映射配置
const colorMap = {
  AB电压: "#A287FF",
  AC电压: "#FFD43B",
  BC电压: "#00E5FF",
};

const shadowColorMap = {
  AB电压: "rgba(162,135,255,0.4)",
  AC电压: "rgba(255,212,59,0.4)",
  BC电压: "rgba(0,229,255,0.4)",
};

function initChart() {
  chartInstance = echarts.init(chartRef.value);

  const xData = Array.from({ length: 20 }, (_, i) => i + 1);

  const option = {
    tooltip: {
      trigger: "axis",
    },
    legend: {
      data: serverData.value.map((d) => d.name),
      right: 30,
      top: 10,
      textStyle: { color: "#fff", fontSize: 14 },
      itemWidth: 10,
      itemHeight: 10,
    },
    grid: {
      left: 30,
      right: 30,
      bottom: 30,
      top: 50,
      containLabel: true,
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: xData,
      axisLine: { lineStyle: { color: "#69cbd3" } },
      axisLabel: { color: "#fff" },
    },
    yAxis: {
      type: "value",
      axisLine: { show: false },
      splitLine: { show: false },
      axisLabel: { color: "#fff" },
    },
    series: serverData.value.map((item) => ({
      name: item.name,
      type: "line",
      smooth: true,
      lineStyle: {
        color: colorMap[item.name] || "#fff",
        width: 3,
        shadowColor: shadowColorMap[item.name] || "rgba(255,255,255,0.3)",
        shadowBlur: 10,
      },
      data: item.Vdata,
    })),
  };

  chartInstance.setOption(option);
}

// 后期的图标更新
watch(serverData, () => {
  if (chartInstance) initChart();
});

onMounted(() => {
  initChart();
});
</script>

<style lang="less" scoped>
// 引入公共样式
@import "@/assets/styles/common.less";
.voltage-chart {
  position: relative;
  background-color: #001a2cb2;
  padding: 20px;
  border-radius: 10px;
  height: 219px;

  .chart {
    width: 100%;
    height: 200px;
  }
}
</style>
