<script lang="ts" setup>
// import { Icon } from '@/components/Icon'
import { useFullscreen } from '@vueuse/core'
import { propTypes } from '@/utils/propTypes'
// import { useDesign } from '@/hooks/web/useDesign'
import fullScreen from '@/assets/icons/fullScreen.png'
defineOptions({ name: 'ScreenFull' })

// const { getPrefixCls } = useDesign()

// const prefixCls = getPrefixCls('screenfull')

defineProps({
  color: propTypes.string.def('')
})

// const { toggle, isFullscreen } = useFullscreen()
const { toggle } = useFullscreen()
const toggleFullscreen = () => {
  toggle()
}
</script>

<template>
  <div @click="toggleFullscreen">
    <!-- <Icon
      :color="color"
      :icon="isFullscreen ? 'zmdi:fullscreen-exit' : 'zmdi:fullscreen'"
      :size="18"
    /> -->
    <img :src="fullScreen" alt="" class="iconPng" />
  </div>
</template>
<style>
.iconPng {
  width: 90px;
  height: 42px;
}
</style>
