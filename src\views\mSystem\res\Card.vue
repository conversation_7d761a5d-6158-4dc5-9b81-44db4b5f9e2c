<script setup lang="ts">
const props = defineProps({
  title: {
    type: String,
    default: '资源单价'
  },
  unit: {
    type: String,
    default: '元'
  },
  value: {
    type: String,
    default: '0.00'
  }
})
const emit = defineEmits(['save'])
// 使用本地响应式变量避免直接修改 props
const value = ref(props.value)
const save = () => {
  emit('save',value.value)
}
</script>

<template>
  <div class="mycard">
    <div style="display: flex">
      <div class="line"></div>
      <div class="text">{{ title }}</div>
    </div>

    <div style="display: flex; margin-top: 20px">
      <div class="text" style="margin-left: 34px">资源单价</div>
      <el-input class="input" v-model="value" />

      <div class="text" style="margin-left: 10px">单位（{{ unit }}）</div>
    </div>

    <el-button class="myButton" @click="save" >保存</el-button>
  </div>
</template>

<style scoped lang="scss">
.mycard {
  margin-top: 20px;
  height: 158px;
  width: 369px;
  background: linear-gradient(180deg, #03163d 0%, #04336a 100%);
  border: 1px solid;
  border-image-source: linear-gradient(180deg, rgba(123, 178, 255, 0) 0%, #7bb2ff 100%);
}

.line {
  margin-left: 20px;
  margin-top: 20px;
  width: 5px;
  height: 17px;
  background: linear-gradient(180deg, #00fff0 0%, #30a8ff 97.3%);
  box-shadow: 2px 2px 4px 0px rgba(0, 44, 57, 0.5421);
}

.text {
  margin-left: 10px;
  margin-top: 20px;
  font-family: OPPOSans, serif;
  font-weight: 400;
  font-size: 16px;
  leading-trim: NONE;
  line-height: 100%;
  letter-spacing: 0px;
  color: rgba(255, 255, 255, 1);
}

.input {
  width: 111px;
  height: 32px;
  border: 1px rgba(66, 180, 244, 1);
  margin-left: 10px;
  margin-top: 13px;
}

.myButton {
  width: 60px;
  height: 28px;
  margin-top: 10px;
  margin-left: auto;
  margin-right: 20px;
  display: flex;
  justify-content: flex-end;
  background: linear-gradient(98.37deg, #17CBF2 0%, #0094FF 100%);
  color: rgba(255, 255, 255, 1);
  border-radius: 0;
}
</style>
