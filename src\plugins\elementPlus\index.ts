import type { App } from 'vue'
// 需要全局引入一些组件，如ElScrollbar，不然一些下拉项样式有问题
import { ElLoading, ElScrollbar, ElButton } from 'element-plus'

import zhCn from "element-plus/es/locale/lang/zh-cn"
const plugins = [ElLoading]

const components = [ElScrollbar, ElButton]

zhCn.el.pagination.total = "共:" + `{total}` + "条"
zhCn.el.pagination.goto = "跳转到"

export const setupElementPlus = (app: App<Element>) => {
  plugins.forEach((plugin) => {
    app.use(plugin)
  })

  components.forEach((component) => {
    app.component(component.name, component)
  })
}
