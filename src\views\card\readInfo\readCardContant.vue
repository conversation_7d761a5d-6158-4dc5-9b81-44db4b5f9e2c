<script setup lang="ts">
import Contant from '@/views/card/readInfo/componets/contant.vue'

import {titleDataType} from '@/views/card/readInfo/type'

const titleData: titleDataType = [
  {
    prop: 'cardId',
    label: '卡号'
  },
  {
    prop: 'cardId',
    label: '卡号'
  },
  {
    prop: 'cardId',
    label: '卡号'
  },
  {
    prop: 'cardId',
    label: '卡号'
  }
]

const JTTitleData: titleDataType = [
  {
    label: 'JT代码',
    prop: 'JTCode'
  },
  {
    label: 'JT代码',
    prop: 'JTCode'
  },
  {
    label: 'JT代码',
    prop: 'JTCode'
  },
  {
    label: 'JT代码',
    prop: 'JTCode'
  },
  {
    label: 'JT代码',
    prop: 'JTCode'
  },
  {
    label: 'JT代码',
    prop: 'JTCode'
  },
  {
    label: 'JT代码',
    prop: 'JTCode'
  }
]

const tableTitleData: titleDataType = [
  {
    label: '控制器代码',
    prop: 'controllerCode'
  },
  {
    label: 'JT代码',
    prop: 'JTCode'
  },
  {
    label: 'JT舰号',
    prop: 'JTName'
  },
  {
    label: 'JT型号',
    prop: 'JTTpe'
  }, {
    label:'JT隶属单位代码',
    prop:'JTUnitCode'
  },
  {
    label: 'JT隶属单位名称',
    prop: 'JTUnitName'
  },
  {
    label: 'JT保障单位代码',
    prop: 'JTProtectionUnitCode'
  },
  {
    label: 'JT保障单位名称',
    prop: 'JTProtectionUnitName'
  },
  {
    label: 'JT母港代码',
    prop: 'JTMotherPortCode'
  },
  {
    label: 'JT母港名称',
    prop: 'JTMotherPortName'
  },

]
</script>

<template>
  <div class="contant">
    <div style="display: flex">
      <Contant :title-data="titleData" />

      <Contant :title-data="JTTitleData" title="JT信息" style="margin-left: 20px" />
    </div>
    <div class="contant-buttongroup">
      <el-button class="contant-button1">读卡</el-button>
      <el-button class="contant-button2">清除卡上记录</el-button>
      <el-button class="contant-button3">数据入库</el-button>
    </div>

    <el-table :data="tableDataList" class="table" height="480px">
      <el-table-column
        :prop="item.prop"
        :label="item.label"
        v-for="(item, index) in tableTitleData"
        :key="index"
      />
    </el-table>
    <div class="page">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="[100, 200, 300, 400]"
        size="large"
        layout="total, sizes, prev, pager, next, jumper"
        :total="400"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.contant {
  margin-left: 20px;
  margin-top: 40px;
  margin-right: 20px;
  color: #ffffff;
}

.contant-buttongroup {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.contant-button1 {
  background: linear-gradient(180deg, #0086bf 0%, #004ea9 100%);
  color: #ffffff;
  border-radius: 0;
}

.contant-button2 {
  background: linear-gradient(180deg, #dfbc41 0%, #f95a00 100%);
  color: #ffffff;
  border-radius: 0;
}

.contant-button3 {
  background: linear-gradient(98.37deg, #17cbf2 0%, #0094ff 100%);
  color: #ffffff;
  border-radius: 0;
}
.page {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}
</style>
