<script lang="tsx">
// 这里使用到的是tsx语法扩展，他允许在脚本里面直接编写类似于html的标记，极大提升了UI界面的便捷性
import { PropType } from 'vue'
import { ElMenu, ElScrollbar } from 'element-plus'
import { useAppStore } from '@/store/modules/app'
import { usePermissionStore } from '@/store/modules/permission'
import { useRenderMenuItem } from './components/useRenderMenuItem'
import { isUrl } from '@/utils/is'
import { useDesign } from '@/hooks/web/useDesign'
import { LayoutType } from '@/types/layout'

//这里获取用户名和头像的相关信息：
import { useUserStore } from '@/store/modules/user'
const userStore = useUserStore()
const username = userStore.getUser.nickname;
const avatar = userStore.getUser.avatar;

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('menu')

export default defineComponent({
  // eslint-disable-next-line vue/no-reserved-component-names
  name: 'Menu',
  props: {
    menuSelect: {
      type: Function as PropType<(index: string) => void>,
      default: undefined
    }
  },
  setup(props) {
    const appStore = useAppStore()

    const layout = computed(() => appStore.getLayout)

    const { push, currentRoute } = useRouter()

    const permissionStore = usePermissionStore()

    const menuMode = computed((): 'vertical' | 'horizontal' => {
      // 竖
      const vertical: LayoutType[] = ['classic', 'topLeft', 'cutMenu']

      if (vertical.includes(unref(layout))) {
        return 'vertical'
      } else {
        return 'horizontal'
      }
    })

    const routers = computed(() =>
      unref(layout) === 'cutMenu' ? permissionStore.getMenuTabRouters : permissionStore.getRouters
    )

    const collapse = computed(() => appStore.getCollapse)

    const uniqueOpened = computed(() => appStore.getUniqueOpened)

    const activeMenu = computed(() => {
      const { meta, path } = unref(currentRoute)
      // if set path, the sidebar will hig./ the path you set
      if (meta.activeMenu) {
        return meta.activeMenu as string
      }
      return path
    })

    const menuSelect = (index: string) => {
      if (props.menuSelect) {
        props.menuSelect(index)
      }
      // 自定义事件
      if (isUrl(index)) {
        window.open(index)
      } else {
        push(index)
      }
    }

    const renderMenuWrap = () => {
      if (unref(layout) === 'top') {
        return renderMenu()
      } else {
        //这里return的内容就是在tsx的特性，直接在脚本部分编写html代码
        return <ElScrollbar>{renderMenu()}</ElScrollbar>
      }
    }
    //这里进行样式扩展：
    // 定义样式对象
    const welcomeTextStyle = {
      fontFamily: 'OPPOSans',
      fontWeight: 400,
      fontSize: '16px',
      lineHeight: '100%',
      letterSpacing: '0px'
    };

    const renderMenu = () => {
      return (
        <div>
          <div
            style={{
              width: '200px',
              height: '86px',
              background: 'linear-gradient(180deg, #0568B0 0%, #01172A 100%)',
              color: 'white', // 假设文字颜色为白色，可按需调整
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '16px', // 假设字体大小，可按需调整
              gap: '15px'
            }}
          >
            <img src={unref(avatar)} alt="" style="width: 43px;height: 43px;top: 90px;left: 21px;border-radius: 50%;" />
            欢迎您，操作员<br />
            {unref(username)}



          </div>
          <ElMenu
            //tsx中编辑标签配置的几个特殊语法:
            //1、这里的unref工具函数可以将响应式数据转换为普通数据，当然你也可以使用.value。但是unref的底层：return isRef(ref) ? ref.value : ref;会去判断你传入的只是否为一个ref对象，有时如果不是ref对象的话，直接使用.value会报错，所以使用unref()会更加安全
            //2、（你的变量必须使用大括号进行包裹）标签属性的值若为 JavaScript 表达式，必须用大括号 {} 包裹。上述写法会让 defaultActive 属性的值被当作字符串 "activeMenu.value"，而非 activeMenu.value 实际计算出的值。 
            defaultActive={unref(activeMenu)}
            mode={unref(menuMode)}
            collapse={
              unref(layout) === 'top' || unref(layout) === 'cutMenu' ? false : unref(collapse)
            }
            uniqueOpened={unref(layout) === 'top' ? false : unref(uniqueOpened)}
            backgroundColor="var(--left-menu-bg-color)"
            textColor="var(--left-menu-text-color)"
            activeTextColor="var(--left-menu-text-active-color)"
            //为菜单的弹出层添加自定义类名。菜单模式为垂直时，添加 {prefixCls}-popper--vertical 类名；为水平时，添加 {prefixCls}-popper--horizontal 类名。
            popperClass={
              unref(menuMode) === 'vertical'
                ? `${prefixCls}-popper--vertical`
                : `${prefixCls}-popper--horizontal`
            }
            onSelect={menuSelect}
          >
            {{
              default: () => {
                const { renderMenuItem } = useRenderMenuItem(unref(menuMode))
                return renderMenuItem(unref(routers))
              }
            }}
          </ElMenu>
        </div>

      )
    }

    return () => (
      <div
        id={prefixCls}
        class={[
          `${prefixCls} ${prefixCls}__${unref(menuMode)}`,
          'h-[100%] overflow-hidden flex-col bg-[var(--left-menu-bg-color)]',
          {
            'w-[var(--left-menu-min-width)]': unref(collapse) && unref(layout) !== 'cutMenu',
            'w-[var(--left-menu-max-width)]': !unref(collapse) && unref(layout) !== 'cutMenu'
          }
        ]}
      >
        {renderMenuWrap()}
      </div>
    )
  }
})
</script>

<style lang="scss" scoped>
$prefix-cls: #{$namespace}-menu;

.#{$prefix-cls} {
  position: relative;
  transition: width var(--transition-time-02);

  :deep(.#{$elNamespace}-menu) {
    width: 100% !important;
    border-right: none;

    // 设置选中时子标题的颜色
    .is-active {
      &>.#{$elNamespace}-sub-menu__title {
        color: var(--left-menu-text-active-color) !important;
      }
    }

    // 设置子菜单悬停的高亮和背景色
    .#{$elNamespace}-sub-menu__title,
    .#{$elNamespace}-menu-item {
      &:hover {
        color: var(--left-menu-text-active-color) !important;
        background-color: var(--left-menu-bg-color) !important;
      }
    }

    // 设置选中时的高亮背景和高亮颜色
    .#{$elNamespace}-menu-item.is-active {
      color: var(--left-menu-text-active-color) !important;
      background-color: #013c69 !important;

      &:hover {
        background-color: #013c69 !important;
      }
    }

    .#{$elNamespace}-menu-item.is-active {
      position: relative;
    }

    // 设置子菜单的背景颜色
    .#{$elNamespace}-menu {

      .#{$elNamespace}-sub-menu__title,
      .#{$elNamespace}-menu-item:not(.is-active) {
        background-color: #01172A !important;
      }
    }
  }

  // 折叠时的最小宽度
  :deep(.#{$elNamespace}-menu--collapse) {
    width: var(--left-menu-min-width);

    &>.is-active,
    &>.is-active>.#{$elNamespace}-sub-menu__title {
      position: relative;
      background-color: var(--left-menu-collapse-bg-active-color) !important;
    }
  }

  // 折叠动画的时候，就需要把文字给隐藏掉
  :deep(.horizontal-collapse-transition) {

    // transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out !important;
    .#{$prefix-cls}__title {
      display: none;
    }
  }

  // 垂直菜单
  &__vertical {
    :deep(.#{$elNamespace}-menu--vertical) {

      &:not(.#{$elNamespace}-menu--collapse) .#{$elNamespace}-sub-menu__title,
      .#{$elNamespace}-menu-item {
        padding-right: 0;
      }
    }
  }

  // 水平菜单
  &__horizontal {
    height: calc(var(--top-tool-height)) !important;

    :deep(.#{$elNamespace}-menu--horizontal) {
      height: calc(var(--top-tool-height));
      border-bottom: none;

      // 重新设置底部高亮颜色
      &>.#{$elNamespace}-sub-menu.is-active {
        .#{$elNamespace}-sub-menu__title {
          border-bottom-color: var(--el-color-primary) !important;
        }
      }

      .#{$elNamespace}-menu-item.is-active {
        position: relative;

        &::after {
          display: none !important;
        }
      }

      .#{$prefix-cls}__title {
        /* stylelint-disable-next-line */
        max-height: calc(var(--top-tool-height) - 2px) !important;
        /* stylelint-disable-next-line */
        line-height: calc(var(--top-tool-height) - 2px);
      }
    }
  }
}
</style>

<style lang="scss">
$prefix-cls: #{$namespace}-menu-popper;

.#{$prefix-cls}--vertical,
.#{$prefix-cls}--horizontal {

  // 设置选中时子标题的颜色
  .is-active {
    &>.el-sub-menu__title {
      color: var(--left-menu-text-active-color) !important;
    }
  }

  // 设置子菜单悬停的高亮和背景色
  .el-sub-menu__title,
  .el-menu-item {
    &:hover {
      color: var(--left-menu-text-active-color) !important;
      background-color: var(--left-menu-bg-color) !important;
    }
  }

  // 设置选中时的高亮背景
  .el-menu-item.is-active {
    position: relative;
    background-color: #013c69 !important;

    &:hover {
      background-color: #013c69 !important;
    }
  }
}
</style>
