<template>
  <!-- 测试部分开始 -->
  <StackedBar />
  <!-- 顶部数据参数，传入四个参数：图片文件名、标题、总数、总数单位 -->
  <TopBar imgSrc="total_el_count.png" title="电箱总数量" count="12" unit="个" />
  <DataPad title="控制电压" count="123.5" unit="V" />
  <BarChart title="电压有效值" :data="serverData" />
  <!-- 测试部分结束 -->
</template>
<script lang="js" setup>
import { ref } from 'vue'
import DataPad from '@/layout/components/DataPad/index.vue'
import TopBar from '@/layout/components/TopBar/index.vue'
import BarChart from '@/layout/components/BarChart/index.vue'
import StackedBar from '@/layout/components/StackedBar/index.vue'
const serverData = ref([
 {property: "A相", value: "120.0"},
 {property: "B相", value: "80.0"},
 {property: "C相", value: "220.0"},
])
</script>
<style lang="less" scoped></style>
