<template>
  <div
    class="mainBox"
    :class="[
      ' w-full  dark:bg-[var(--el-bg-color)]',
      '!min-h-[calc(97.5vh-var(--top-tool-height)-var(--tags-view-height)-var(--app-footer-height))] pb-0',
    ]"
  >
    <div class="UseTotalCount">
      <!-- 组件传递参数：类型types:number水0 电1  时间单位times:string年Y/月M/日D  用量useCount:number -->
      <Usage useCount="123" :types="1" times="Y" />
      <Usage useCount="123" :types="1" times="M" />
      <Usage useCount="123" :types="1" times="D" />
      <Usage useCount="456" :types="0" times="Y" />
      <Usage useCount="456" :types="0" times="M" />
      <Usage useCount="456" :types="0" times="D" />
    </div>
    <!-- 中部显示终端数量部分 -->
    <div class="MechineCount" v-for="(item, index) in machineCount" :key="index">
      <img :src="`/src/assets/icons/Machine_${index + 1}.png`" alt="" />
      <div class="data">
        <p>{{ item.count }}</p>
        <p>{{ item.description }}</p>
      </div>
    </div>

    <!-- 这是留白部分，没有实际意义，撑开高度使用 -->
    <div class="blank" style="height: 30px"></div>

    <!-- 这里是图标和相应的电压曲线-->
    <!-- 饼图，显示终端控制情况，传递两个参数（echarts的渲染数据、图标），使用一个插槽（标题插槽） -->
    <div class="line1">
      <!-- 这是第一行：控制系统、舰艇情况、电压情况 -->
      <pieChart icons="cloud">
        <template #title> 终端控制系统 </template>
      </pieChart>
      <pieChart icons="ship" :chartData="chartData">
        <template #title> J艇情况 </template>
      </pieChart>
      <!-- 表格曲线，你可以配置对应的电压数据和名称，下面是配置的结果 -->
      <LineChart>
        <template #title> 电压曲线 </template>
      </LineChart>

      <!-- <LineChart :serverData="serverData" /> -->
    </div>

    <div class="blank" style="height: 30px"></div>

    <!-- 这是第二行：舰艇情况、电压曲线 -->
    <div class="line2">
      <!-- 这是表格，记载舰艇信息 -->
      <Table :tableData="tableData" />
      <!-- 电压折线图 -->
      <LineChart>
        <template #title> 电流曲线 </template>
      </LineChart>
    </div>
  </div>
</template>
<!-- 用松弛的js来放过自己把 -->
<script lang="js" setup>
import Usage from '@/layout/components/tags/usage.vue'
import pieChart from '@/layout/components/PieChart/index.vue'
import LineChart from '@/layout/components/LineChart/index.vue'
import Table from '@/layout/components/Table/index.vue'
import {ref,reactive} from 'vue'

//存放假数据的开始
//电压折线图的假数据
let chartData = ref([
  { value: 1, name: "Active", color: "#00E5FF", label: "常规舰艇数" },
  { value: 1, name: "Idle", color: "#FFD700", label: "外港舰艇数" },
  { value: 1, name: "Offline", color: "#00FF97", label: "非供舰艇数" },
],)

//表格假数据
const tableData = ref([
  {
    id: 1,
    code: "123456",
    shipNumber: "12345",
    model: "12345",
    resourceType: "Ⅰ",
    startTime: "2025.01.01 00:00:00",
    endTime: "2025.01.01 00:00:00",
    usage: "456",
  },
  {
    id: 2,
    code: "123456",
    shipNumber: "12345",
    model: "12345",
    resourceType: "Ⅰ",
    startTime: "2025.01.01 00:00:00",
    endTime: "2025.01.01 00:00:00",
    usage: "456",
  },
  {
    id: 3,
    code: "123456",
    shipNumber: "12345",
    model: "12345",
    resourceType: "Ⅱ",
    startTime: "2025.01.01 00:00:00",
    endTime: "2025.01.01 00:00:00",
    usage: "456",
  },
]);


//存放假数据的结束
let machineCount = ref([
  {
    count: 14,
    description: '电箱总数'
  },
  {
    count: 12,
    description: '水箱总数'
  },
  {
    count: 7,
    description: '控制终端总数'
  }
])
</script>
<style lang="less" scoped>
/* 全局样式，使 el-scrollbar__wrap 高度占满屏幕 */
.mainBox {
  // height: 91vh !important;
}
// 这里使用less语法的特性将特殊的字体设为一个类(那个蓝白渐变的字体)
.colorfulText {
  font-style: normal;
  font-weight: 700;
  font-size: 22px;
  line-height: 26px;
  /* identical to box height */
  letter-spacing: 1px;
  background: linear-gradient(180deg, #ffffff 33.2%, #00c2ff 80.77%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

// 这里实现奇妙的居中效果
.mainBox {
  position: relative;
  height: inherit;
  width: inherit;
  background-image: url(@/assets/imgs/mainBg.png);
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;

  .UseTotalCount {
    display: flex;
    justify-content: space-between;
    margin-bottom: 39px;
  }

  .MechineCount {
    display: flex;
    margin: 23px 10px;
    // position: relative;
    img {
      width: 46px;
      height: 50px;
    }
    .data {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-left: 25.73px;
      width: 120px;
      p {
        text-align: center;
      }
      & > p:nth-child(1) {
        width: 30px;
        height: 26px;
        margin-bottom: 5px;
        .colorfulText();
      }
      & > p:nth-child(2) {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 14px;
        line-height: 100%;
        letter-spacing: 0px;
        text-align: center;
        color: #ffffff;
      }
    }
  }

  .line1 {
    display: grid;
    // width: clac(100vw - 220px);
    grid-template-columns: 1fr 1fr 2fr;
    grid-template-rows: 1fr;
    gap: 20px;
  }

  .line2 {
    display: grid;
    // width: clac(100vw - 220px);
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr;
    gap: 20px;
  }
}
</style>
<style lang="less"></style>
