<template>
  <div class="topBar">
    <img :src="`/src/assets/icons/${imgSrc}`" alt="" />
    <div class="Title">{{ title }}</div>
    <div class="count">
      <span>{{ count }}</span>
      <span>{{ unit }}</span>
    </div>
  </div>
</template>
<script setup>
// 从父组件接收四个参数：图片文件名、标题、总数、总数单位
defineProps({
  imgSrc: {
    type: String,
    default: 'total_el_count.png'
  },
  title: {
    type: String,
    default: '电箱总数量'
  },
  count: {
    type: Number,
    default: 12
  },
  unit: {
    type: String,
    default: '个'
  }
})
</script>

<style lang="less" scoped>
.topBar {
  position: relative;
  width: 476px;
  height: 98px;
  border-width: 1px;

  background: linear-gradient(180deg, rgba(44, 155, 235, 0.45) 0%, rgba(0, 33, 96, 0.29) 100%);
  backdrop-filter: blur(2px);
  img {
    position: absolute;
    top: 17.45px;
    left: 10px;
  }
  .Title {
    position: absolute;
    top: 31.4px;
    left: 160px;
    font-family: Microsoft YaHei;
    font-weight: 700;
    font-size: 20px;
    line-height: 30px;
    letter-spacing: 1px;
    text-align: center;
    color: #ffffff;
  }
  .count {
    /* 225.6 */

    position: absolute;
    top: 30.45px;
    right: 64.48px;
    font-family: 'Sz Number';
    font-style: normal;
    font-weight: 600;
    font-size: 34px;
    line-height: 100%;
    /* identical to box height, or 34px */
    background: linear-gradient(180deg, #bafff4 0%, #15c7ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    & > span:nth-child(2) {
      margin-left: 0.5rem;
      font-size: 18px;
    }
  }
}
</style>
