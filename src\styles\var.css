:root {
  --login-bg-color: #293146;

  --left-menu-max-width: 200px;

  --left-menu-min-width: 64px;

  /* 修改侧边栏背景颜色 */
  --left-menu-bg-color: #01172a;

  --left-menu-bg-light-color: #01172a;

  /* 修改菜单选中时的背景颜色 */
  --left-menu-bg-active-color: #013c69;

  --left-menu-text-color: #bfcbd9;

  --left-menu-text-active-color: #00a3ff !important;

  --left-menu-collapse-bg-active-color: var(--el-color-primary);
  /* left menu end */

  /* logo start */
  --logo-height: 50px;

  --logo-title-text-color: #fff;
  /* logo end */

  /* header start */
  --top-header-bg-color: '#fff';

  --top-header-text-color: 'inherit';

  --top-header-hover-color: #f6f6f6;

  --top-tool-height: var(--logo-height);

  --top-tool-p-x: 0;

  --tags-view-height: 35px;
  /* header start */

  /* tab menu start */
  --tab-menu-max-width: 80px;

  --tab-menu-min-width: 30px;

  --tab-menu-collapse-height: 36px;
  /* tab menu end */

  --app-content-padding: 20px;

  --app-content-bg-color: #0f234b;

  --app-footer-height: 50px;

  --transition-time-02: 0.2s;
}

.dark {
  --app-content-bg-color: var(--el-bg-color);
}

html,
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

*,
:after,
:before {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
