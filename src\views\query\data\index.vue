<script setup lang="ts">
import { ref } from 'vue'
import { Delete, Edit } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const selectData = ref('')

const tableDataList = ref([
  {
    index: 1,
    time: '2024-01-15 10:30:00',
    code: 'CTRL001',
    type: '网络故障',
    content: '网络连接超时'
  },
  {
    index: 2,
    time: '2024-01-15 11:45:00',
    code: 'CTRL002',
    type: '硬件故障',
    content: '传感器异常'
  },
  {
    index: 3,
    time: '2024-01-15 14:20:00',
    code: 'CTRL003',
    type: '软件故障',
    content: '程序运行错误'
  }
])

// 分页相关
const titleData = [
  {
    label: '事件时间',
    prop: 'time'
  },
  {
    label: '控制器代码',
    prop: 'code'
  },
  {
    label: '故障类型',
    prop: 'type'
  },
  {
    label: '故障内容',
    prop: 'content'
  }
]

// props 或模拟数据
const totalItems = ref(0) // 总条数
const pageSize = ref(10)
const currentPage = ref(1)
const jumpPage = ref('')

// 模拟从父组件或后端获取的数据长度
watch(
  () => totalItems.value,
  () => {
    if (currentPage.value > totalPages.value) {
      currentPage.value = totalPages.value || 1
    }
  }
)

// 总页数
const totalPages = computed(() => Math.ceil(totalItems.value / pageSize.value))

// 切换函数
function toFirstPage() {
  currentPage.value = 1
}
function prevPage() {
  if (currentPage.value > 1) currentPage.value--
}
function goToPage() {
  ElMessage({
    message: '跳转成功',
    type: 'success'
  })
  const num = parseInt(jumpPage.value)
  if (!isNaN(num) && num >= 1 && num <= totalPages.value) {
    currentPage.value = num
  }
}

// 这里对斑马纹颜色进行配置，也就是修改对应的背景颜色
const tableRowStyle = (row) => {
  return row.rowIndex % 2 === 0
    ? { background: 'rgba(0, 26, 44, 0.7)' }
    : { background: 'rgba(23, 40, 96, 1) ' }
}

// 在js部分去修改对应的select选择框,获取ref对象
// const select = ref()
// select.value.style.borderRadius = '0'
</script>

<template>
  <div class="error-container">
    <el-form>
      <el-row :gutter="20" justify="start">
        <el-col :span="6">
          <el-form-item label="日期范围">
            <el-date-picker
              v-model="selectData"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
            />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="用户卡ID">
            <el-select ref="select">
              <el-option label="ces1" value="1" />
              <el-option label="ces2" value="2" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="4">
          <el-form-item label="JT代码">
            <el-select>
              <el-option label="ces1" value="1" />
              <el-option label="ces2" value="2" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="4">
          <el-form-item label="卡类型">
            <el-select>
              <el-option label="ces1" value="1" />
              <el-option label="ces2" value="2" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="4">
          <el-form-item label="消耗类型">
            <el-select>
              <el-option label="ces1" value="1" />
              <el-option label="ces2" value="2" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div style="display: flex; justify-content: end">
      <el-button class="data-button1">高级配置</el-button>
      <el-button class="data-button2">查询</el-button>
    </div>

    <div class="error-content">
      <el-table
        :data="tableDataList"
        height="600px"
        :row-style="tableRowStyle"
        row-class-name="rowStyle"
        header-row-class-name="headerRowStyle"
        header-cell-class-name="headerCellStyle"
        cell-class-name="cellStyle"
      >
        <el-table-column prop="index" label="序号" />
        <el-table-column
          :prop="item.prop"
          :label="item.label"
          v-for="(item, index) in titleData"
          :key="index"
        />
      </el-table>
    </div>

    <!-- <div class="page">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="[10, 20, 30, 40]"
        size="default"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tableDataList.length"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div> -->

    <!-- 这里是分页逻辑，可将其转化为组件 -->
    <div class="page">
      <span
        >总共 {{ totalPages }} 页 &nbsp;当前第 {{ currentPage }} 页 &nbsp;共
        {{ totalItems }} 条&nbsp;</span
      >

      <el-button class="page-btn" :disabled="currentPage === 1" @click="toFirstPage">
        <img src="@/assets/icons/arrLeft.png" alt="" />&nbsp;上一页
      </el-button>

      <el-button class="page-btn" :disabled="currentPage === 1" @click="prevPage">
        下一页&nbsp;<img src="@/assets/icons/arrRight.png" alt="" />
      </el-button>

      <span class="jumper-text">跳转到</span>
      <el-input v-model="jumpPage" style="width: 50px" />

      <span class="jumper-text">页</span>

      <button @click="goToPage" class="jump-btn"> 跳&nbsp;&nbsp;&nbsp;转 </button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.error-container {
  margin: 20px;
}

.data-button1 {
  width: 65px;
  height: 30px;
  background: linear-gradient(180deg, #0086bf 0%, #004ea9 100%);
  border-radius: 0px;
  color: #ffffff;
  border-color: rgba(66, 180, 244, 1);
}

.data-button2 {
  width: 65px;
  height: 30px;
  background: linear-gradient(180deg, #00bfb3 0%, #0076a9 100%);
  border-radius: 0px;
  color: #ffffff;
  border-color: #409eff;
}

.error-content {
  margin-top: 50px;
}

//这里进行修改table表格的样式内容：
// 使用深度选择器穿透 scoped 样式
:deep(.headerRowStyle) {
  background: rgba(23, 40, 96, 1);
  border: none;
}

:deep(.headerCellStyle) {
  color: rgba(22, 157, 255, 1);
  font-family: 'Microsoft YaHei', sans-serif;
  font-weight: 700;
  font-size: 16px; // 30px 太大了，建议改小
  line-height: 1.2;
  letter-spacing: 0px;
  text-align: center;
}

// // 斑马纹配置：奇数行和偶数行
// :deep(.el-table__row) {
//   background: rgba(23, 40, 96, 1) !important; // 奇数行（默认行）
// }

// :deep(.el-table__row.el-table__row--striped) {
//   background: rgba(0, 26, 44, 0.7) !important; // 偶数行（斑马纹行）
// }

// // 悬停效果
// :deep(.el-table__row:hover) {
//   background: rgba(30, 50, 120, 1) !important; // 悬停时的颜色
// }

// :deep(.el-table__row.el-table__row--striped:hover) {
//   background: rgba(10, 36, 54, 0.9) !important; // 斑马纹行悬停时的颜色
// }

:deep(.cellStyle) {
  text-align: center;
  font-family: 'Microsoft YaHei', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.2;
  letter-spacing: 0px;
  text-align: center;
  padding: 12px 8px;
  color: #fff; // 白色文字，适配深色背景
}

// 这里修改选择框的样式
:deep(.el-select__wrapper) {
  border: 1px solid rgba(66, 180, 244, 1);
  border-radius: 0px;
}
:deep(.el-popper) {
  // background: rgba(0, 26, 44, 0.7);
  // border: 1px solid rgba(66, 180, 244, 1);
  border-radius: 0px;
  color: red;
}
.page {
  margin-top: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  // 设置字体样式
  font-family: OPPOSans;
  font-weight: 300;
  font-style: Medium;
  font-size: 16px;
  leading-trim: NONE;
  line-height: 100%;
  letter-spacing: 1px;
  color: rgba(255, 255, 255, 1);
}

.jump-btn {
  width: 65px;
  height: 30px;
  background: linear-gradient(180deg, #0086bf 0%, #004ea9 100%);
  border: 0px;
  border-radius: 4px;
  color: rgba(255, 255, 255, 1);
}

.page-btn {
  border: 1px solid rgba(66, 180, 244, 1);
  background: rgba(0, 36, 60, 1);
  color: rgba(66, 180, 244, 1);
  padding: 10px !important;
}
.page-btn img {
  width: 10px;
  height: 10px;
}
.jumper-text {
  margin: 0px 10px;
}
</style>
