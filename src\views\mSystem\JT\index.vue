<script setup lang="ts">
import CommonTitle from '@/components/commonTitle/commonTitle.vue'
import { Delete, Edit } from '@element-plus/icons-vue'
import { ref } from 'vue'

const titleData = [
  {
    prop: 'JTId',
    label: 'JT代码'
  },
  {
    prop: 'JT',
    label: 'JT舷号'
  },
  {
    prop: 'JTType',
    label: 'JT型号'
  },
  {
    prop: 'JTSubCode',
    label: 'JT隶属单位代码'
  },
  {
    prop: 'JTSubName',
    label: 'JT隶属单位名称'
  },
  {
    prop: 'JTGuaranteeCode',
    label: 'JT保障单位代码'
  },
  {
    prop: 'JTGuaranteeName',
    label: 'JT保障单位名称'
  },
  {
    prop: 'JTMotherPortCode',
    label: 'JT母港代码'
  },
  {
    prop: 'JTMotherPortName',
    label: 'JT母港名称'
  }
]
const tableDataList = ref([
  {
    JTId: '1',
    JT: '1',
    JTType: '1',
    JTSubCode: '1',
    JTSubName: '1',
    JTGuaranteeCode: '1',
    JTGuaranteeName: '1',
    JTMotherPortCode: '1',
    JTMotherPortName: '1'
  }
])

const tableEdit = () => {
  console.log('tableEdit')
}

const tableDelete = () => {
  console.log('tableDelete')
}
</script>

<template>
  <div class="contant">
   <CommonTitle text="JT配置" />
    <!--居右边按钮-->
    <div class="buttonGroup">
      <el-button color="linear-gradient(180deg, #5ee9f2 0%, #0f8188 100%);" class="leftbutton"
        >新增
      </el-button>
      <el-button color="linear-gradient(180deg, #5fcfff 0%, #0069e3 100%);" class="rightButton">
        清空
      </el-button>
    </div>
    <div  style="margin-top: 20px">
      <el-form label-width="120px">
        <el-row :gutter="100">
          <el-col :span="8" v-for="(item, index) in titleData" :key="index">
            <el-form-item :label="item.label">
              <el-input placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <el-divider style="border: 1px solid #1669a5" />

    <div>
      <el-input placeholder="请输入内容" style="width: 294px"/>
      <el-button
        color="linear-gradient(180deg, #5fcfff 0%, #0069e3 100%);"
        class="rightButton"
        style="margin-left: 10px"
      >
        查询
      </el-button>
      <el-button
        color="linear-gradient(180deg, #5fcfff 0%, #0069e3 100%);"
        class="rightButton"
        style="width: 130px"
      >
        导入港口实例
      </el-button>
    </div>

    <el-table :data="tableDataList" class="table" height="600px">
      <el-table-column
        :prop="item.prop"
        :label="item.label"
        v-for="(item, index) in titleData"
        :key="index"
      />
      <el-table-column label="操作" align="center">
        <template #default>
          <el-icon color="#00A3FF" :size="20" @click="tableEdit()">
            <Edit />
          </el-icon>
          <el-icon color="#EB3838" :size="20" style="margin-left: 10px" @click="tableDelete()">
            <Delete />
          </el-icon>
        </template>
      </el-table-column>
    </el-table>

    <div class="page">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="[100, 200, 300, 400]"
        size="large"
        layout="total, sizes, prev, pager, next, jumper"
        :total="400"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.contant {
  margin-top: 17px;
  margin-left: 34px;
  margin-right: 20px;
}

.buttonGroup {
  display: flex;
  justify-content: flex-end;
}

.leftbutton {
  background: linear-gradient(180deg, #5ee9f2 0%, #0f8188 100%);
  border-color: #0f234b;
  width: 88px;
  height: 33px;
}

.rightButton {
  background: linear-gradient(180deg, #5fcfff 0%, #0069e3 100%);
  border-color: #0f234b;
  width: 88px;
  height: 33px;
}

.table {
  margin-top: 20px;
}

.page {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}
</style>
