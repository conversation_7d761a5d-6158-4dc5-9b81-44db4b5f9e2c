<script setup lang="ts">
import { ref } from 'vue'

const readCardstate = ref(false)
const cardstate = ref(false)

const getClass = (state) => {
  return state ? 'mycard-success' : 'mycard-error'
}
</script>

<template>
  <div class="card-top-contant">
    <div class="first">
      <span class="text">读卡器状态</span>
      <div :class="getClass(readCardstate)">{{ readCardstate ? '已连接' : '未连接' }}</div>
    </div>

    <div class="first" style="margin-left: 50px">
      <span class="text">卡片状态</span>
      <div :class="getClass(cardstate)">{{ cardstate ? '已查卡' : '未插卡' }}</div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.card-top-contant {
  display: flex;
  font-family: OPPOSans, serif;
  font-weight: 400;
  font-size: 16px;
  leading-trim: NONE;
  line-height: 100%;
  letter-spacing: 0;
}

.first {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  user-select: none;
}

.text {
}

.mycard-error {
  margin-left: 10px;
  width: 74px;
  height: 30px;
  opacity: 1;
  border-radius: 4px;
  background: rgba(255, 0, 0, 0.3);
  text-align: center;
  line-height: 30px;
  color: rgba(255, 41, 41, 1);
  border: 1px solid rgba(161, 31, 31, 1);
}

.mycard-success {
  margin-left: 10px;
  width: 74px;
  height: 30px;
  opacity: 1;
  border-radius: 4px;
  background: rgba(255, 0, 0, 0.3);
  text-align: center;
  line-height: 30px;
  color: rgba(255, 41, 41, 1);
  border: 1px solid rgba(161, 31, 31, 1);
}
</style>
