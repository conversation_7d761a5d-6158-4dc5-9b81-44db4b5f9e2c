<template>
  <div class="jboat-table-container">
    <el-scrollbar>
      <el-table
        :data="props.tableData"
        style="width: 100%; height: 100%"
        class="jboat-table"
        :header-cell-style="headerCellStyle"
        :cell-style="cellStyle"
        :row-style="rowStyle"
      >
        <el-table-column prop="id" label="序号" width="80" />
        <el-table-column prop="code" label="J艇代码" width="120" />
        <el-table-column prop="shipNumber" label="J艇舰号" width="120" />
        <el-table-column prop="model" label="J艇型号" width="120" />
        <el-table-column prop="resourceType" label="资源类型" width="120" />
        <el-table-column prop="startTime" label="开始时间" width="180" />
        <el-table-column prop="endTime" label="结束时间" width="180" />
        <el-table-column prop="usage" label="用量" width="100" />
      </el-table>
    </el-scrollbar>
  </div>
</template>

<script setup>
import { ref } from "vue";

// 表格数据，从父组件里面来
const props = defineProps({
  tableData: {
    type: Array,
    default: () => [],
  },
});

// 表头样式
const headerCellStyle = () => {
  return {
    backgroundColor: "#0f234b",
    color: "#169DFF",
    border: "1px solid #0f234b",
    fontWeight: 700,
    fontSize: "16px",
    letterSpacing: "0px",
    textAlign: "center",
  };
};

// 单元格样式
const cellStyle = () => {
  return {
    backgroundColor: "transparent",
    color: "#FFFFFF",
    fontWeight: 400,
    fontSize: "14px",
    lineHeight: "100%",
    letterSpacing: "0px",
    border: "1px solid #1a3a5a",
    textAlign: "center",
  };
};

// 行样式
const rowStyle = ({ rowIndex }) => {
  return {
    backgroundColor: rowIndex % 2 === 0 ? "rgba(26, 58, 90, 0.3)" : "transparent",
    "&:hover": {
      backgroundColor: "rgba(79, 195, 247, 0.1)",
    },
  };
};
</script>

<style lang="less" scoped>
.jboat-table-container {
  width: 43.5vw;
  height: 100%;
  background: #001a2cb2;
  padding: 10px;
  // border: 2px solid #1a3a5a;
  border-radius: 4px;

  // 修改tabel的内置属性
  :deep(.el-scrollbar__view) {
    height: 100%;
  }

  :deep(.el-table) {
    background-color: transparent;

    &::before {
      background-color: #0f234b;
    }

    th.el-table__cell {
      background-color: red;
    }

    tr {
      background-color: transparent;
    }

    .el-table__row--striped {
      background: #172860;
    }

    .el-table__row:hover {
      background-color: rgba(79, 195, 247, 0.1) !important;
    }
  }
}
</style>
