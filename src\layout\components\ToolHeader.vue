<script lang="tsx">
import { defineComponent, computed } from 'vue'
// import { UserInfo } from '@/layout/components/UserInfo'
import { Screenfull } from '@/layout/components/Screenfull'
import TenantVisit from '@/layout/components/TenantVisit/index.vue'
import { useAppStore } from '@/store/modules/app'
import { useDesign } from '@/hooks/web/useDesign'
import { checkPermi } from '@/utils/permission'
// import { ElMessageBox } from 'element-plus'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { useUserStore } from '@/store/modules/user'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n' // 确保导入 useI18n
import exit from '@/assets/icons/exit.png'


const { getPrefixCls, variables } = useDesign()
const prefixCls = getPrefixCls('tool-header')

export default defineComponent({
  name: 'ToolHeader',
  setup() {
    const appStore = useAppStore()
    // 全屏图标
    const screenfull = computed(() => appStore.getScreenfull)
    // 租户切换权限
    const hasTenantVisitPermission = computed(
      () => import.meta.env.VITE_APP_TENANT_ENABLE === 'true' && checkPermi(['system:tenant:visit'])
    )

    const userStore = useUserStore()
    const tagsViewStore = useTagsViewStore()
    const { replace } = useRouter() // 在 setup 函数内调用 useRouter
    const { t } = useI18n()

    // 退出登录方法
    const loginOut = async () => {
      try {
        await ElMessageBox.confirm(t('common.loginOutMessage'), t('common.reminder'), {
          confirmButtonText: t('common.ok'),
          cancelButtonText: t('common.cancel'),
          type: 'warning'
        })
        await userStore.loginOut()
        tagsViewStore.delAllViews()
        replace('/login?redirect=/index')
      } catch { }
    }

    return () => (
      <div
        id={`${variables.namespace}-tool-header`}
        class={[
          prefixCls,
        ]}
        style="height: 70px; background-color:#001529; margin-left:-2px"
      >
        <div class="stayRight">
          {hasTenantVisitPermission.value ? <TenantVisit /> : undefined}
          {screenfull.value ? (
            <Screenfull class="custom-hover" color="var(--top-header-text-color)"></Screenfull>
          ) : undefined}
          {/* <UserInfo></UserInfo> */}
          {/* 退出功能按键 ，这里的报错是因为编辑器不认识他。。。 在tsx中，组件必须使用驼峰命名法，要不然无法运行*/}
          <img src={exit} alt="" class="iconPng"
            style={{ color: 'var(--top-header-text-color)' }}
            onClick={loginOut} />

        </div>
      </div>
    )
  }
})
</script>

<style lang="scss" scoped>
$prefix-cls: #{$namespace}-tool-header;

.#{$prefix-cls} {
  transition: left var(--transition-time-02);
}

.height {
  height: 70px;
}

.stayRight {
  height: inherit;
  display: flex;
  align-items: center;
  justify-content: right;
}

// 这个是图标退出的样式
.iconPng {
  width: 90px;
  height: 42px;
  margin-right: 20px;
}
</style>
