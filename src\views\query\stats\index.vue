<script setup lang="ts">
import PieChart from '@/layout/components/PieChart/index.vue'
import LineChart from '@/layout/components/LineChart/index.vue'
import StackedBar from '@/layout/components/StackedBar/index.vue'
import { ref } from 'vue'

let chartData1 = ref([
  { value: 1, name: 'Active', color: '#00E5FF', label: '常规舰艇数' },
  { value: 1, name: 'Idle', color: '#FFD700', label: '外港舰艇数' },
  { value: 1, name: 'Offline', color: '#00FF97', label: '非供舰艇数' }
])

let chartData2 = ref([
  { value: 1, name: 'Active', color: '#00E5FF', label: '常规舰艇数' },
  { value: 1, name: 'Idle', color: '#FFD700', label: '外港舰艇数' },
  { value: 1, name: 'Offline', color: '#00FF97', label: '非供舰艇数' }
])

let chartData3 = ref([
  { value: 1, name: 'Active', color: '#00E5FF', label: '常规舰艇数' },
  { value: 1, name: 'Idle', color: '#FFD700', label: '外港舰艇数' },
  { value: 1, name: 'Offline', color: '#00FF97', label: '非供舰艇数' }
])

let testingData1 = ref([
  {
    name: 'AB电压',
    Vdata: [25, 22, 18, 15, 13, 14, 16, 18, 21, 24, 27, 29, 27, 25, 22, 19, 16, 15, 17, 20]
  }
])
let testingData2 = ref([
  {
    name: 'AB电压',
    Vdata: [10, 13, 17, 19, 20, 18, 16, 15, 13, 12, 10, 11, 13, 14, 15, 16, 17, 19, 21, 2]
  }
])
</script>

<template>
  <div class="dashboard-container">
    <el-row :gutter="20">
      <el-col :span="8">
        <PieChart icons="cloud" :chart-data="chartData1">
          <template #title>本日电消耗JT概况</template>
        </PieChart>
      </el-col>

      <el-col :span="8">
        <PieChart icons="cloud" :chart-data="chartData2">
          <template #title>本月电消耗JT概况</template>
        </PieChart>
      </el-col>

      <el-col :span="8">
        <PieChart icons="cloud" :chart-data="chartData3">
          <template #title>本年电消耗JT概况</template>
        </PieChart>
      </el-col>
    </el-row>
  </div>

  <!-- 网格布局，分为2*2布局 -->
  <div class="dashboard-container">
    <!-- 上面两个是统计表 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <StackedBar />
      </el-col>
      <el-col :span="12">
        <StackedBar />
      </el-col>
    </el-row>

    <!-- 留白 -->
    <div style="height: 40px"></div>

    <!-- 下面是两个折线图 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <LineChart :chartData="testingData1">
          <template #title>JT岸电消耗计量—按年月分析</template>
        </LineChart>
      </el-col>

      <el-col :span="12">
        <LineChart :chart-data="testingData2"
          ><template #title>JT淡水消耗计量—按年月分析</template>
        </LineChart>
      </el-col>
    </el-row>
  </div>
</template>

<style scoped lang="scss">
.dashboard-container {
  margin: 10px 20px;
}
</style>
