// DataPollingBase - 导入一个值（类）
// type DataPollingConfig - 导入一个类型（接口/类型别名） type 关键字明确表示这是一个仅类型导入
import { DataPollingBase, type DataPollingConfig } from './DataPollingBase'
import { getUserPage } from '@/api/system/user'

/**
 * 用户数据轮询类
 * 基于 DataPollingBase 的具体实现示例
 */
export class UserDataPolling extends DataPollingBase<any, PageParam> {
  //                                                   ↑     ↑
  //                                               用户数据  分页参数
  constructor(initialParams: Partial<PageParam> = {}, config: DataPollingConfig = {}) {
    super(initialParams, {
      interval: 1000, // 每1秒请求一次
      immediate: true,
      autoStart: true,
      continueOnError: true,
      ...config
    })

    // 自动初始化轮询
    this.initPolling()
  }

  /**
   * 实现抽象方法：调用用户列表API
   */
  protected async fetchData(params: PageParam): Promise<PageResult<any>> {
    try {
      const result = await getUserPage(params)

      // 验证返回数据格式
      if (!result || typeof result !== 'object') {
        throw new Error('API返回数据格式不正确')
      }

      // 确保返回的数据有正确的结构
      return {
        list: Array.isArray(result.list) ? result.list : [],
        total: typeof result.total === 'number' ? result.total : 0,
        ...result
      }
    } catch (error) {
      console.error('getUserPage API调用失败:', error)
      throw error
    }
  }

  /**
   * 重写数据更新回调
   */
  protected onDataUpdated(result: PageResult<any>): void {
    console.log(`用户数据已更新，共 ${result.total} 条记录`)
  }

  /**
   * 重写错误处理回调
   */
  protected onError(error: Error): void {
    console.error('获取用户数据失败:', error.message)
    // 可以在这里添加错误提示逻辑
  }
}

/**
 * 创建用户数据轮询实例的工厂函数
 */
export function createUserDataPolling(
  initialParams: Partial<PageParam> = {},
  config: DataPollingConfig = {}
): UserDataPolling {
  return new UserDataPolling(initialParams, config)
}
