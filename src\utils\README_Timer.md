# 定时器工具使用指南

本项目提供了一套完整的定时器解决方案，用于处理需要定期请求数据的场景。

## 功能特性

- 🔄 **自动轮询**: 支持定时自动请求数据
- ⏱️ **灵活配置**: 可配置轮询间隔、是否立即执行等
- 🛡️ **错误处理**: 内置错误处理机制，支持错误时继续/停止轮询
- 🎯 **类型安全**: 完整的 TypeScript 类型支持
- 🧹 **自动清理**: 组件卸载时自动清理定时器，防止内存泄漏
- 📊 **状态管理**: 提供加载状态、错误状态等响应式数据

## 使用方式

### 方式一：使用 Composable 函数（推荐）

适用于简单的定时任务和数据轮询场景。

```typescript
import { useTimer, useDataPolling } from '@/utils/timer'

// 简单定时器
const timer = useTimer(
  () => {
    console.log('定时器执行')
  },
  {
    interval: 1000, // 每1秒执行
    immediate: true, // 立即执行一次
    autoStart: true  // 自动开始
  }
)

// 数据轮询
const polling = useDataPolling(
  async () => {
    const response = await fetch('/api/data')
    return response.json()
  },
  { interval: 2000 }
)
```

### 方式二：使用数据轮询基类

适用于需要复杂数据处理逻辑的场景。

```typescript
import { DataPollingBase } from '@/utils/DataPollingBase'

class MyDataPolling extends DataPollingBase<UserVO, PageParam> {
  protected async fetchData(params: PageParam) {
    return await getUserPage(params)
  }
  
  protected onDataUpdated(result: PageResult<UserVO>) {
    console.log('数据已更新:', result.total)
  }
}

// 使用
const polling = new MyDataPolling(
  { pageNo: 1, pageSize: 10 },
  { interval: 1000 }
)
```

### 方式三：继承定时器基类

适用于需要面向对象设计的复杂场景。

```typescript
import { TimerBase } from '@/utils/timer'

class MyTimer extends TimerBase {
  constructor() {
    super({ interval: 1000 })
    this.initTimer()
  }
  
  protected async onTimer() {
    // 定时器逻辑
    await this.doSomething()
  }
}
```

## API 参考

### useTimer

基础定时器 Composable 函数。

```typescript
function useTimer(
  callback: () => void | Promise<void>,
  config?: TimerConfig
): TimerReturn
```

### useDataPolling

数据轮询 Composable 函数。

```typescript
function useDataPolling<T>(
  fetchFunction: () => Promise<T>,
  config?: TimerConfig
): TimerReturn & {
  loading: Ref<boolean>
  error: Ref<Error | null>
  data: Ref<T | null>
}
```

### DataPollingBase

数据轮询基类。

```typescript
abstract class DataPollingBase<TData, TParams> {
  // 响应式数据
  readonly data: Ref<TData[]>
  readonly total: Ref<number>
  readonly loading: Ref<boolean>
  readonly error: Ref<Error | null>
  readonly queryParams: TParams
  
  // 控制方法
  start(): void
  stop(): void
  restart(): void
  refresh(): Promise<void>
  updateParams(params: Partial<TParams>): void
  
  // 抽象方法（子类必须实现）
  protected abstract fetchData(params: TParams): Promise<PageResult<TData>>
}
```

### TimerConfig

定时器配置接口。

```typescript
interface TimerConfig {
  interval?: number     // 间隔时间（毫秒），默认 1000
  immediate?: boolean   // 是否立即执行，默认 true
  autoStart?: boolean   // 是否自动开始，默认 true
}
```

## 实际使用示例

### 用户列表实时更新

```vue
<template>
  <div>
    <el-button @click="togglePolling">
      {{ userPolling.isRunning ? '停止' : '开始' }}轮询
    </el-button>
    
    <el-table :data="userPolling.data" :loading="userPolling.loading">
      <!-- 表格列定义 -->
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { UserDataPolling } from '@/utils/UserDataPolling'

const userPolling = new UserDataPolling(
  { pageNo: 1, pageSize: 10 },
  { interval: 1000 }
)

const togglePolling = () => {
  if (userPolling.isRunning) {
    userPolling.stop()
  } else {
    userPolling.start()
  }
}
</script>
```

### 系统状态监控

```typescript
import { useDataPolling } from '@/utils/timer'

const systemStatus = useDataPolling(
  async () => {
    const response = await fetch('/api/system/status')
    return response.json()
  },
  { interval: 5000 } // 每5秒检查一次
)

// 在模板中使用
// systemStatus.data.value - 系统状态数据
// systemStatus.loading.value - 加载状态
// systemStatus.error.value - 错误信息
```

## 注意事项

1. **内存泄漏防护**: 所有定时器都会在组件卸载时自动清理
2. **错误处理**: 默认情况下，出错时会继续轮询，可通过 `continueOnError: false` 改变此行为
3. **性能考虑**: 合理设置轮询间隔，避免过于频繁的请求
4. **网络优化**: 建议在网络错误时适当增加轮询间隔

## 最佳实践

1. 使用 Composable 函数处理简单场景
2. 使用基类处理复杂的数据管理场景
3. 在组件中合理控制轮询的开始和停止
4. 根据业务需求调整轮询间隔
5. 添加适当的错误处理和用户提示
