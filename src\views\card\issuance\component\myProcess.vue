<script setup lang="ts">

const props = defineProps({
  percentage: {
    type: String,
    default: '10'
  }
})
</script>

<template>
  <div style="display: flex; align-items: center;font-size: 14px">
    <span>制卡进度</span>
    <el-progress
      class="progress-warp"
      :percentage="10"
      stroke-width="20"
      style="width: 300px; margin-left: 10px"
    />
  </div>
</template>

<style scoped lang="scss">


.progress-warp {
  // 背景色
  :deep(.el-progress-bar__outer) {
    background: rgba(32, 90, 115, 1);
    border-radius: 0;
  }

  // 渐变进度条色
  :deep(.el-progress-bar__inner) {
    background: linear-gradient(90deg, #205A73 0%, #14C8EF 100%);
    border-radius: 0;
  }
  :deep(.el-progress__text) {
    margin-left: 20px;
    font-size: 14px;
  }
}
</style>
