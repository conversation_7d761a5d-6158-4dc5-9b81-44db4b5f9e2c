import { ref, reactive, type Ref } from 'vue'
import { useDataPolling, type TimerConfig } from './timer'

/**
 * 数据轮询基类配置接口
 */
export interface DataPollingConfig extends TimerConfig {
  /** 初始查询参数 */
  initialParams?: Record<string, any>
  /** 是否在出错时继续轮询，默认 true */
  continueOnError?: boolean
}

/**
 * 数据轮询基类
 * 专门用于需要定期请求后端数据的组件
 */
export abstract class DataPollingBase<TData = any, TParams = PageParam> {
  /** 数据列表 */
  public readonly data: Ref<TData[]> = ref([])

  /** 总数据量 */
  public readonly total: Ref<number> = ref(0)

  /** 加载状态 */
  public readonly loading: Ref<boolean> = ref(false)

  /** 错误信息 */
  public readonly error: Ref<Error | null> = ref(null)

  /** 查询参数 */
  public readonly queryParams: TParams

  /** 轮询配置 */
  protected readonly config: DataPollingConfig

  /** 数据轮询实例 */
  protected pollingInstance: ReturnType<typeof useDataPolling> | null = null

  constructor(initialParams: Partial<TParams> = {}, config: DataPollingConfig = {}) {
    // 默认配置
    this.config = {
      interval: 1000, // 默认1秒轮询
      immediate: true,
      autoStart: true,
      continueOnError: true,
      ...config
    }

    // 初始化查询参数
    this.queryParams = reactive({
      ...this.getDefaultParams(),
      ...initialParams
    }) as TParams
  }

  /**
   * 获取默认查询参数
   * 子类可以重写此方法来提供默认参数
   */
  protected getDefaultParams(): Partial<TParams> {
    return {
      pageNo: 1,
      pageSize: 10
    } as unknown as Partial<TParams>
  }

  /**
   * 抽象方法：获取数据的API调用
   * 子类必须实现此方法
   */
  protected abstract fetchData(params: TParams): Promise<PageResult<TData>>

  /**
   * 数据获取的包装函数
   */
  private async wrappedFetchData(): Promise<void> {
    try {
      const result = await this.fetchData(this.queryParams)

      // 确保数据格式正确
      if (result && typeof result === 'object') {
        // 更新数据，确保 list 是数组
        const list = Array.isArray(result.list) ? result.list : []
        const total = typeof result.total === 'number' ? result.total : 0

        this.data.value = list
        this.total.value = total
        this.error.value = null

        // 调用数据更新回调
        this.onDataUpdated(result)
      } else {
        // 如果返回的不是预期的对象格式，设置为空数据
        this.data.value = []
        this.total.value = 0
        this.error.value = new Error('API返回数据格式不正确')
      }
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err))
      this.error.value = error

      // 设置空数据
      this.data.value = []
      this.total.value = 0

      // 调用错误处理回调
      this.onError(error)

      // 如果配置为出错时不继续轮询，则停止轮询
      if (!this.config.continueOnError) {
        this.stop()
      }
    }
  }

  /**
   * 数据更新回调
   * 子类可以重写此方法来处理数据更新后的逻辑
   */
  protected onDataUpdated(_result: PageResult<TData>): void {
    // 默认空实现，子类可以重写
  }

  /**
   * 错误处理回调
   * 子类可以重写此方法来处理错误
   */
  protected onError(error: Error): void {
    console.error('Data polling error:', error)
  }

  /**
   * 初始化轮询
   */
  public initPolling(): void {
    if (this.pollingInstance) {
      this.pollingInstance.destroy()
    }

    this.pollingInstance = useDataPolling(() => this.wrappedFetchData(), this.config)

    // 同步加载状态
    this.loading.value = this.pollingInstance.loading.value
  }

  /**
   * 开始轮询
   */
  public start(): void {
    if (!this.pollingInstance) {
      this.initPolling()
    }
    this.pollingInstance?.start()
  }

  /**
   * 停止轮询
   */
  public stop(): void {
    this.pollingInstance?.stop()
  }

  /**
   * 重启轮询
   */
  public restart(): void {
    this.pollingInstance?.restart()
  }

  /**
   * 销毁轮询
   */
  public destroy(): void {
    this.pollingInstance?.destroy()
    this.pollingInstance = null
  }

  /**
   * 手动刷新数据（不影响轮询状态）
   */
  public async refresh(): Promise<void> {
    await this.wrappedFetchData()
  }

  /**
   * 更新查询参数并重新开始轮询
   */
  public updateParams(newParams: Partial<TParams>): void {
    Object.assign(this.queryParams as any, newParams)
    this.restart()
  }

  /**
   * 重置查询参数到默认值
   */
  public resetParams(): void {
    const defaultParams = this.getDefaultParams()
    Object.assign(this.queryParams as any, defaultParams)
    this.restart()
  }

  /**
   * 获取轮询运行状态
   */
  public get isRunning(): boolean {
    return this.pollingInstance?.isRunning.value || false
  }

  /**
   * 设置轮询间隔
   */
  public setInterval(interval: number): void {
    this.config.interval = interval
    if (this.isRunning) {
      this.restart()
    }
  }
}
