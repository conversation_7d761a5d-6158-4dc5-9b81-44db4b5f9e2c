<template>
  <div class="simple-polling-example">
    <h3>简单轮询示例</h3>
    
    <!-- 使用 Composable 方式 -->
    <el-card title="方式一：使用 Composable">
      <div class="example-content">
        <p>数据: {{ pollingData.data }}</p>
        <p>加载状态: {{ pollingData.loading ? '加载中' : '空闲' }}</p>
        <p>轮询状态: {{ pollingData.isRunning ? '运行中' : '已停止' }}</p>
        
        <div class="controls">
          <el-button @click="pollingData.start()">开始</el-button>
          <el-button @click="pollingData.stop()">停止</el-button>
          <el-button @click="pollingData.restart()">重启</el-button>
        </div>
      </div>
    </el-card>

    <!-- 使用基类方式 -->
    <el-card title="方式二：使用基类" style="margin-top: 20px;">
      <div class="example-content">
        <p>用户总数: {{ userPolling.total }}</p>
        <p>当前数据条数: {{ userPolling.data.length }}</p>
        <p>轮询状态: {{ userPolling.isRunning ? '运行中' : '已停止' }}</p>
        
        <div class="controls">
          <el-button @click="userPolling.start()">开始</el-button>
          <el-button @click="userPolling.stop()">停止</el-button>
          <el-button @click="userPolling.refresh()">手动刷新</el-button>
        </div>
      </div>
    </el-card>

    <!-- 使用定时器基类方式 -->
    <el-card title="方式三：继承定时器基类" style="margin-top: 20px;">
      <div class="example-content">
        <p>计数器: {{ counter }}</p>
        <p>轮询状态: {{ customTimer?.isRunning ? '运行中' : '已停止' }}</p>
        
        <div class="controls">
          <el-button @click="customTimer?.start()">开始</el-button>
          <el-button @click="customTimer?.stop()">停止</el-button>
          <el-button @click="resetCounter">重置计数器</el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onUnmounted } from 'vue'
import { useDataPolling, TimerBase } from '@/utils/timer'
import { UserDataPolling } from '@/utils/UserDataPolling'
import { getUserPage } from '@/api/system/user'

/** 简单轮询示例组件 */
defineOptions({ name: 'SimplePollingExample' })

// ========== 方式一：使用 Composable ==========
const pollingData = useDataPolling(
  async () => {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    return { timestamp: new Date().toLocaleTimeString(), random: Math.random() }
  },
  {
    interval: 2000, // 每2秒执行一次
    immediate: true,
    autoStart: true
  }
)

// ========== 方式二：使用数据轮询基类 ==========
const userPolling = new UserDataPolling(
  { pageNo: 1, pageSize: 5 },
  { 
    interval: 3000, // 每3秒执行一次
    autoStart: true 
  }
)

// ========== 方式三：继承定时器基类 ==========
const counter = ref(0)

class CounterTimer extends TimerBase {
  constructor() {
    super({
      interval: 1000, // 每1秒执行一次
      immediate: true,
      autoStart: true
    })
    this.initTimer()
  }

  protected async onTimer() {
    counter.value++
    console.log('计数器:', counter.value)
  }
}

const customTimer = new CounterTimer()

/**
 * 重置计数器
 */
const resetCounter = () => {
  counter.value = 0
}

// 组件卸载时清理所有定时器
onUnmounted(() => {
  pollingData.destroy()
  userPolling.destroy()
  customTimer.destroy()
})
</script>

<style scoped>
.simple-polling-example {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.example-content {
  padding: 15px 0;
}

.example-content p {
  margin: 8px 0;
  font-size: 14px;
}

.controls {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

.el-card {
  margin-bottom: 20px;
}

.el-card :deep(.el-card__header) {
  background-color: #f5f7fa;
  font-weight: 600;
}
</style>
