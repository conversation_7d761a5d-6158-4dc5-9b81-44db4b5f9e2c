<script setup lang="ts">
import CommonTitle from '@/components/commonTitle/commonTitle.vue'
import {Delete, Edit} from "@element-plus/icons-vue";

import {ref} from "vue"

const titleData = [
  {
    prop: 'supplyUnitCode',
    label: '供应单位代码'
  },
  {
    prop: 'supplyUnitName',
    label: '供应单位名称'
  },
  {
    prop: 'supplyPortCode',
    label: '供应港点代码'
  },
  {
    prop: 'supplyPortName',
    label: '供应港点名称'
  },
  {
    prop: 'supplyDockCode',
    label: '供应码头代码'
  },
  {
    prop: 'supplyDockName',
    label: '供应码头名称'
  }
]

const tableDataList = ref([
  {
    supplyUnitCode: '0001',
    supplyUnitName: '上海港',
    supplyPortCode: '0001',
    supplyPortName: '上海港',
    supplyDockCode: '0001',
    supplyDockName: '上海港'
  }
])
</script>
<template>
  <div class="contant">

    <CommonTitle text="港口配置" />

    <!--居右边按钮-->
    <div class="buttonGroup">
      <el-button color="linear-gradient(180deg, #5ee9f2 0%, #0f8188 100%);" class="leftbutton"
        >新增
      </el-button>
      <el-button color="linear-gradient(180deg, #5fcfff 0%, #0069e3 100%);" class="rightButton">
        清空
      </el-button>
    </div>

    <div style="margin-top: 20px">
      <el-form label-width="120px">
        <el-row :gutter="100">
          <el-col :span="8" v-for="(item, index) in titleData" :key="index">
            <el-form-item :label="item.label">
              <el-input placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <el-divider style="border: 1px solid #1669a5" />

    <div>
      <el-input placeholder="请输入内容" style="width: 294px" />
      <el-button
        color="linear-gradient(180deg, #5fcfff 0%, #0069e3 100%);"
        class="rightButton"
        style="margin-left: 10px"
      >
        查询
      </el-button>
      <el-button
        color="linear-gradient(180deg, #5fcfff 0%, #0069e3 100%);"
        class="rightButton"
        style="width: 130px"
      >
        导入港口实例
      </el-button>
    </div>

    <el-table :data="tableDataList" class="table" height="600px">
      <el-table-column type="index" label="序号" width="80" align="center" />
      <el-table-column
        :prop="item.prop"
        :label="item.label"
        v-for="(item, index) in titleData"
        :key="index"
      />
      <el-table-column label="操作" align="center">
        <template #default>
          <el-icon color="#00A3FF" :size="20" @click="tableEdit()">
            <Edit />
          </el-icon>
          <el-icon color="#EB3838" :size="20" style="margin-left: 10px" @click="tableDelete()">
            <Delete />
          </el-icon>
        </template>
      </el-table-column>
    </el-table>

    <div class="page">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="[100, 200, 300, 400]"
        size="large"
        layout="total, sizes, prev, pager, next, jumper"
        :total="400"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

  </div>
</template>

<style scoped lang="scss">
.contant {
  margin-top: 17px;
  margin-left: 34px;
  margin-right: 20px;
}

.buttonGroup {
  display: flex;
  justify-content: flex-end;
}

.leftbutton {
  background: linear-gradient(180deg, #5ee9f2 0%, #0f8188 100%);
  border-color: #0f234b;
  width: 88px;
  height: 33px;
}

.rightButton {
  background: linear-gradient(180deg, #5fcfff 0%, #0069e3 100%);
  border-color: #0f234b;
  width: 88px;
  height: 33px;
}


.page {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}
</style>
