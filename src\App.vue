<script lang="ts" setup>
 import { onMounted} from "vue";
 import { isDark } from '@/utils/is'
import { useAppStore } from '@/store/modules/app'
import { useDesign } from '@/hooks/web/useDesign'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import routerSearch from '@/components/RouterSearch/index.vue'

defineOptions({ name: 'APP' })

const { getPrefixCls } = useDesign()
const prefixCls = getPrefixCls('app')
const appStore = useAppStore()
const currentSize = computed(() => appStore.getCurrentSize)
const greyMode = computed(() => appStore.getGreyMode)
const { wsCache } = useCache()

// 根据浏览器当前主题设置系统主题色
const setDefaultTheme = () => {
  let isDarkTheme = wsCache.get(CACHE_KEY.IS_DARK)
  if (isDarkTheme === null) {
    isDarkTheme = isDark()
  }
  appStore.setIsDark(isDarkTheme)
}
setDefaultTheme()

 onMounted(() => {
   //修改默认背景颜色
   document.body.style.setProperty('--el-fill-color-blank', '#0f234b');
   document.body.style.setProperty('--el-bg-color', '#0f234b');
   document.body.style.setProperty('--el-bg-color-overlay', '#0f234b');
   document.body.style.setProperty('--el-fill-color-light', '#0f234b');
   document.body.style.setProperty('--el-fill-color', '#0f234b');
   document.body.style.setProperty('--el-mask-color', '#0f234b');
   //修改默认字体颜色
   document.body.style.setProperty('--el-text-color-secondary', '#169DFF');
   document.body.style.setProperty('--el-text-color-primary', '#ffffff');
   document.body.style.setProperty('--el-text-color-regular', '#ffffff');

   //修改默认边框颜色
   document.body.style.setProperty('--el-border-color', '#217DD1');
   document.body.style.setProperty('--el-input-hover-border-color', '#217DD1');

   document.body.style.setProperty('--el-border-color-lighter', '#217DD1');
 })
</script>


<template>
  <ConfigGlobal :size="currentSize">
    <RouterView :class="greyMode ? `${prefixCls}-grey-mode` : ''" />
    <routerSearch />
  </ConfigGlobal>
</template>
<style lang="scss">
@import url('https://fonts.googleapis.com/css2?family=OPPOSans&display=swap');
$prefix-cls: #{$namespace}-app;

.size {
  width: 100%;
  height: 100%;
}

html,
body {
  @extend .size;
  font-family: OPPOSans,serif;
  padding: 0 !important;
  margin: 0;
  overflow: hidden;
  #app {
    @extend .size;
  }
}

.#{$prefix-cls}-grey-mode {
  filter: grayscale(100%);
}
</style>
