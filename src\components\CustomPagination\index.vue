<template>
  <div class="page">
    <span
      >总共 {{ totalPages }} 页 &nbsp;当前第 {{ currentPage }} 页 &nbsp;共
      {{ total }} 条&nbsp;</span
    >
    <el-button class="page-btn" :disabled="currentPage === 1" @click="handlePrevPage">
      <img src="@/assets/icons/arrLeft.png" alt="" />&nbsp;上一页
    </el-button>

    <el-button class="page-btn" :disabled="currentPage === totalPages" @click="handleNextPage">
      下一页&nbsp;<img src="@/assets/icons/arrRight.png" alt="" />
    </el-button>

    <span class="jumper-text">跳转到</span>
    <el-input v-model="jumpPageInput" style="width: 50px" @keyup.enter="handleJumpToPage" />

    <span class="jumper-text">页</span>

    <button @click="handleJumpToPage" class="jump-btn"> 跳&nbsp;&nbsp;&nbsp;转 </button>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { ref, computed, watch } from 'vue'

/** 自定义分页组件 */
defineOptions({ name: 'CustomPagination' })

// Props 定义
interface Props {
  /** 当前页码 */
  currentPage: number
  /** 每页条数 */
  pageSize: number
  /** 总条数 */
  total: number
}

const props = withDefaults(defineProps<Props>(), {
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// Emits 定义
interface Emits {
  /** 页码改变事件 */
  (e: 'current-change', page: number): void
  /** 每页条数改变事件 */
  (e: 'size-change', size: number): void
}

const emit = defineEmits<Emits>()

// 跳转页码输入框
const jumpPageInput = ref<string>('')

// 计算总页数
const totalPages = computed(() => {
  return Math.ceil(props.total / props.pageSize)
})

// 上一页
const handlePrevPage = () => {
  if (props.currentPage > 1) {
    emit('current-change', props.currentPage - 1)
  }
}

// 下一页
const handleNextPage = () => {
  if (props.currentPage < totalPages.value) {
    emit('current-change', props.currentPage + 1)
  }
}

// 跳转到指定页
const handleJumpToPage = () => {
  const page = parseInt(jumpPageInput.value)

  // 验证页码有效性
  if (isNaN(page) || page < 1 || page > totalPages.value) {
    jumpPageInput.value = ''
    ElMessage.error('请输入有效页码')
    return
  }

  // 如果页码没有变化，不触发事件
  if (page === props.currentPage) {
    jumpPageInput.value = ''
    return
  }

  emit('current-change', page)
  jumpPageInput.value = ''
}

// 监听当前页变化，清空跳转输入框
watch(
  () => props.currentPage,
  () => {
    jumpPageInput.value = ''
  }
)
</script>

<style scoped lang="scss">
.page {
  margin-top: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  // 设置字体样式
  font-family: OPPOSans;
  font-weight: 300;
  font-style: Medium;
  font-size: 16px;
  leading-trim: NONE;
  line-height: 100%;
  letter-spacing: 1px;
  color: rgba(255, 255, 255, 1);
}

.jump-btn {
  width: 65px;
  height: 30px;
  background: linear-gradient(180deg, #0086bf 0%, #004ea9 100%);
  border: 0px;
  border-radius: 4px;
  color: rgba(255, 255, 255, 1);
}

.page-btn {
  border: 1px solid rgba(66, 180, 244, 1);
  background: rgba(0, 36, 60, 1);
  color: rgba(66, 180, 244, 1);
  padding: 10px !important;
}
.page-btn img {
  width: 10px;
  height: 10px;
}
.jumper-text {
  margin: 0px 10px;
}
</style>
