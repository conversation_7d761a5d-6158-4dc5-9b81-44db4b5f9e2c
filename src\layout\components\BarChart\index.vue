<template>
  <div class="barChart">
    <div class="titles">
      <slot name="title">控制终端概况</slot>
    </div>
    <div class="datas" v-for="(item, index) in data" :key="item.property">
      <p>{{ item.value }}</p>
      <img
        :src="`/src/assets/icons/${imgIndex[index % imgIndex.length]}`"
        alt=""
        :height="Math.min(Number(item.value) * 0.65, 230) + 'px'"
      />
      <p>{{ item.property }}</p>
    </div>
  </div>
</template>
<script setup>
// 接收从父组件来的两个参数：标题名、测量数据(数组，每个里面有属性property、数据value)
const props = defineProps({
  title: {
    type: String,
    default: '三相电压有效值'
  },
  data: {
    type: Array,
    default: () => [
      { property: 'A相', value: '120.0' },
      {
        property: 'B相',
        value: '240.0'
      },
      {
        property: 'C相',
        value: '150.0'
      }
    ]
  }
})
//顺序存储不同颜色的图片
const imgIndex = ref(['redBar.png', 'greenBar.png', 'yellowBar.png'])
</script>
<style lang="less">
@import '@/assets/styles/common.less';
.barChart {
  width: (95% / 3 - 446px);
  height: 236px;
  position: relative;
  background: #001a2cb2;
  display: flex;
  justify-content: space-evenly;
  align-items: flex-end;
  padding-bottom: 5px;
  .datas {
    p {
      font-family: OPPOSans;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      letter-spacing: 0px;
      text-align: center;
      vertical-align: bottom;
      color: aliceblue;
    }
    img {
      width: 52px;
    }
  }
}
</style>
