<script setup lang="ts">
import CommonTitle from '@/components/commonTitle/commonTitle.vue'
import Card from '@/views/mSystem/res/Card.vue'

const title = ref('交流岸电')

const value = ref('2.4')
// 单位
const unit = ref('元/KWh')

const save = (value) => {
  console.log(value)
}
</script>

<template>
  <div class="contant">
    <CommonTitle text="资源配置" />
    <div class="cardGoup">
      <Card :title="title" :value="value" :unit="unit" @save="save" />
      <Card title="淡水" value="5.3" unit="元/m³" @save="save" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.contant {
  margin-top: 17px;
  margin-left: 34px;
  margin-right: 20px;
}

.cardGoup {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
}
</style>
