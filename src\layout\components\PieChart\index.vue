<template>
  <div class="terminal-overview">
    <!-- 下面是标题，配置一个具名插槽 -->
    <div class="titles">
      <slot name="title">控制终端概况</slot>
    </div>
    <div ref="chartRef" class="chart"></div>
    <!-- 这里放一个表格中的图标 -->
    <img :src="`/src/assets/icons/${icons}.png`" alt="" />
    <ul class="legend" v-if="!isSimple">
      <li v-for="item in legendData" :key="item.name">
        <span class="color-box" :style="{ backgroundColor: item.color }"></span>
        <span class="label">{{ item.label }}</span>
        <span class="value">{{ item.value }}</span>
      </li>
    </ul>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import * as echarts from 'echarts'

const { chartData, title, icons } = defineProps({
  chartData: {
    type: Array,
    default: () => [
      { value: 1, name: 'Active', color: '#00E5FF', label: '在用终端数' },
      { value: 1, name: 'Idle', color: '#FFD700', label: '空闲终端数' },
      { value: 1, name: 'Offline', color: '#00FF97', label: '离线终端数' }
    ]
  },
  icons: {
    type: String,
    default: 'cloud'
  },
  title: {
    type: String,
    default: '控制终端概况'
  },
  isSimple: {
    //只显示图的情况
    type: Boolean,
    default: false
  }
})

const chartRef = ref()

const legendData = chartData

onMounted(() => {
  const chart = echarts.init(chartRef.value)

  chart.setOption({
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        type: 'pie',
        radius: ['70%', '85%'],
        avoidLabelOverlap: false,
        label: { show: false },
        labelLine: { show: false },
        data: chartData.map((item) => ({
          value: item.value,
          name: item.label,
          itemStyle: { color: item.color }
        }))
      }
    ]
  })
  if (chartRef.value) {
    chartRef.value.style.position = 'fixed'
  }
})
</script>

<style lang="less" scoped>
// 统一配置字体样式
// 引入公共样式
@import '@/assets/styles/common.less';
.terminal-overview {
  position: relative;
  display: flex;
  align-items: center;
  // width: 400px;
  height: 219px;
  background: #001a2cb2;
  backdrop-filter: blur(6px);
  padding: 20px;
  border-radius: 8px;
  color: #fff;

  img {
    position: absolute;
    width: 48px;
    height: 35px;
    left: 53px;
    top: 100px;
  }

  .chart {
    position: absolute;
    left: 20px;
    top: 65px;
    width: 110px !important;
    height: 110px !important;
  }
  .chart-simple {
    margin: 0 auto;
    display: flex;
    justify-content: center;
  }

  .legend {
    position: absolute;
    left: 150px;
    height: 87px;

    list-style: none;
    margin-left: 40px;
    padding: 0;

    li {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .color-box {
        width: 12px;
        height: 12px;
        margin-right: 8px;
        border-radius: 2px;
      }

      .label {
        flex: 1;
        font-size: 14px;
      }

      .value {
        font-size: 16px;
        font-weight: bold;
        margin-left: 8px;
      }
    }
  }
}
</style>
