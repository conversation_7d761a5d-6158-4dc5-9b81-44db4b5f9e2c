<template>
  <div class="test-polling">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>定时器测试组件</span>
          <el-tag :type="userPolling.isRunning ? 'success' : 'danger'">
            {{ userPolling.isRunning ? '运行中' : '已停止' }}
          </el-tag>
        </div>
      </template>

      <div class="controls">
        <el-button 
          :type="userPolling.isRunning ? 'danger' : 'primary'"
          @click="togglePolling"
        >
          {{ userPolling.isRunning ? '停止轮询' : '开始轮询' }}
        </el-button>
        
        <el-button @click="userPolling.refresh()">
          手动刷新
        </el-button>
        
        <el-input-number
          v-model="interval"
          :min="500"
          :max="10000"
          :step="500"
          @change="updateInterval"
          style="width: 150px; margin-left: 10px;"
        />
        <span style="margin-left: 5px;">毫秒</span>
      </div>

      <div class="status" style="margin-top: 15px;">
        <p><strong>数据状态:</strong></p>
        <ul>
          <li>总记录数: {{ userPolling.total }}</li>
          <li>当前数据条数: {{ userPolling.data.length }}</li>
          <li>加载状态: {{ userPolling.loading ? '加载中' : '空闲' }}</li>
          <li>错误信息: {{ userPolling.error?.message || '无' }}</li>
        </ul>
      </div>

      <div v-if="userPolling.error" class="error-info" style="margin-top: 15px;">
        <el-alert
          :title="userPolling.error.message"
          type="error"
          :closable="false"
        />
      </div>

      <div class="data-preview" style="margin-top: 15px;">
        <p><strong>数据预览 (前3条):</strong></p>
        <el-table 
          :data="userPolling.data.slice(0, 3)" 
          :loading="userPolling.loading"
          size="small"
          border
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="username" label="用户名" />
          <el-table-column prop="nickname" label="昵称" />
          <el-table-column prop="status" label="状态">
            <template #default="{ row }">
              <el-tag :type="row.status === 0 ? 'success' : 'danger'">
                {{ row.status === 0 ? '正常' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onUnmounted } from 'vue'
import { UserDataPolling } from '@/utils/UserDataPolling'

/** 定时器测试组件 */
defineOptions({ name: 'TestPolling' })

const interval = ref(2000)

// 创建用户数据轮询实例
const userPolling = new UserDataPolling(
  {
    pageNo: 1,
    pageSize: 10
  },
  {
    interval: interval.value,
    immediate: true,
    autoStart: true,
    continueOnError: true
  }
)

/**
 * 切换轮询状态
 */
const togglePolling = () => {
  if (userPolling.isRunning) {
    userPolling.stop()
  } else {
    userPolling.start()
  }
}

/**
 * 更新轮询间隔
 */
const updateInterval = (newInterval: number) => {
  userPolling.setInterval(newInterval)
}

// 组件卸载时清理资源
onUnmounted(() => {
  userPolling.destroy()
})
</script>

<style scoped>
.test-polling {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status ul {
  list-style: none;
  padding: 0;
}

.status li {
  padding: 5px 0;
  border-bottom: 1px solid #eee;
}

.status li:last-child {
  border-bottom: none;
}
</style>
