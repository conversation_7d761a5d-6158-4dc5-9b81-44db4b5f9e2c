<template>
  <div class="deviceTree">
    <p>选择设备</p>
    <!-- 搜索框 -->
    <el-input v-model="filterText" placeholder="选择设备" :prefix-icon="Search" />

    <!-- 树形结构 -->
    <el-tree
      ref="treeRef"
      :data="treeData"
      :props="defaultProps"
      :filter-node-method="filterNode"
      node-key="id"
      highlight-current
      :default-expand-all="true"
      class="tree"
    >
      <template #default="{ node, data }">
        <span class="custom-tree-node">
          <el-icon :size="14">
            <component :is="iconMap[data.type] || 'Folder'" />
          </el-icon>
          <span :class="{ highlight: node.label.includes(filterText) && filterText }">
            {{ node.label }}
          </span>
        </span>
      </template>
    </el-tree>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { Search, Folder, Ship, Connection, Lightning, Location } from '@element-plus/icons-vue'

const filterText = ref('')
const treeRef = ref(null)

const iconMap = {
  port: Location,
  dock: Ship,
  group: Connection,
  box: Lightning
}

// 假数据结构（对应图中结构）
const treeData = [
  {
    id: 1,
    label: 'XX港口',
    type: 'port',
    children: [
      {
        id: 2,
        label: 'XX号码头',
        type: 'dock',
        children: [
          {
            id: 3,
            label: '400V-1号电箱7A',
            type: 'box'
          },
          {
            id: 4,
            label: '400V-2号电箱7A',
            type: 'box'
          },
          {
            id: 5,
            label: '400V-3号电箱7A',
            type: 'box'
          },
          {
            id: 6,
            label: '400V-4号电箱7A',
            type: 'box'
          }
        ]
      },
      {
        id: 7,
        label: 'XX号码头',
        type: 'dock',
        children: [
          {
            id: 8,
            label: '400V-1号电箱7A',
            type: 'box'
          },
          {
            id: 9,
            label: '400V-2号电箱7A',
            type: 'box'
          },
          {
            id: 10,
            label: '400V-3号电箱7A',
            type: 'box'
          },
          {
            id: 11,
            label: '400V-4号电箱7A',
            type: 'box'
          }
        ]
      }
    ]
  }
]

// 树组件属性
const defaultProps = {
  children: 'children',
  label: 'label'
}

// 监听输入框变化，触发筛选
watch(filterText, (val) => {
  treeRef.value.filter(val)
})

// 筛选逻辑
function filterNode(value, data) {
  if (!value) return true
  return data.label.toLowerCase().includes(value.toLowerCase())
}
</script>

<style scoped lang="less">
.deviceTree {
  background-color: #001828;
  padding: 0 5px;
  color: white;
  font-size: 14px;
  width: 300px;
  height: 100%;
  p {
    height: 47px;
    line-height: 47px;
    font-weight: 400;
    font-size: 16px;
    letter-spacing: 4px;
    text-indent: 1em;
    vertical-align: middle;
    background: url('/src/assets/icons/bg_line.png');
    background-size: 100% 100%;
  }

  .search-box {
    margin-bottom: 12px;

    :deep(.el-input__inner) {
      background-color: transparent;
      border: 1px solid #00e5ff;
      color: white;
    }

    :deep(.el-input__wrapper) {
      background-color: #002033;
    }
  }

  .tree {
    background-color: transparent;

    :deep(.el-tree-node__content) {
      padding-left: 8px;
    }

    .custom-tree-node {
      display: flex;
      align-items: center;
      gap: 6px;

      .highlight {
        color: #00e5ff;
      }
    }
  }
}
</style>
