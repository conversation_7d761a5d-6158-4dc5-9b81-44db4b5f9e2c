<template>
  <div class="power-chart">
    <div class="header">
      <div class="titles">
        <slot name="title">控制终端概况</slot>
      </div>
      <el-select
        v-model="selected"
        size="small"
        class="filter"
        popper-class="operateDropOption"
        @change="fetchData"
      >
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>
    <div ref="chartRef" class="chart"></div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import * as echarts from 'echarts'
import { ElSelect, ElOption } from 'element-plus'
import 'element-plus/es/components/select/style/css'
import 'element-plus/es/components/option/style/css'

// 下拉框选项
const options = [
  { label: '全部', value: 'all' },
  { label: '常规T', value: 'regular' },
  { label: '外港T', value: 'outport' },
  { label: '非供T', value: 'nongong' }
]

const selected = ref('all')
const chartRef = ref()
let chartInstance = null

// 模拟后端获取数据
function getMockData(filter) {
  const base = [300, 320, 310, 290, 280, 270, 260, 250, 240, 230]
  const today = base.map((v) => v + Math.floor(Math.random() * 30))
  const month = base.map((v) => v + 80 + Math.floor(Math.random() * 20))
  const year = base.map((v) => v + 120 + Math.floor(Math.random() * 20))
  return {
    labels: Array(10).fill('XX'),
    today,
    month,
    year
  }
}

function fetchData() {
  const data = getMockData(selected.value)
  updateChart(data)
}

function updateChart(data) {
  if (!chartInstance) return

  chartInstance.setOption({
    xAxis: {
      data: data.labels
    },
    series: [
      { name: '本日', data: data.today },
      { name: '本月', data: data.month },
      { name: '本年', data: data.year }
    ]
  })
}

onMounted(() => {
  chartInstance = echarts.init(chartRef.value)
  chartInstance.setOption({
    backgroundColor: '#001828',
    tooltip: { trigger: 'axis' },
    legend: {
      data: ['本日', '本月', '本年'],
      top: 10,
      right: 120,
      textStyle: { color: '#fff' }
    },
    grid: {
      left: 40,
      right: 20,
      bottom: 40,
      top: 60
    },
    xAxis: {
      type: 'category',
      axisLine: { lineStyle: { color: '#69cbd3' } },
      axisLabel: { color: '#fff' }
    },
    yAxis: {
      type: 'value',
      axisLabel: { color: '#fff' },
      splitLine: { show: false }
    },
    series: [
      {
        name: '本日',
        type: 'bar',
        stack: 'total',
        barWidth: 30,
        data: []
      },
      {
        name: '本月',
        type: 'bar',
        stack: 'total',
        itemStyle: {
          color: '#FFD43B'
        },
        data: []
      },
      {
        name: '本年',
        type: 'bar',
        stack: 'total',
        itemStyle: {
          color: '#00E5FF'
        },
        data: []
      }
    ]
  })

  fetchData()
})
</script>

<style lang="less" scoped>
@import '@/assets/styles/common.less';
.power-chart {
  position: relative;
  background-color: #001828;
  border-radius: 8px;
  padding: 20px;
  max-width: 843px;
  // height: min(50%, 300px);

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      font-size: 18px;
      font-weight: bold;
      color: white;
      display: flex;
      align-items: center;

      .line {
        width: 6px;
        height: 16px;
        background-color: #00e5ff;
        margin-right: 8px;
        border-radius: 2px;
      }

      .unit {
        font-size: 12px;
        color: #aaa;
        margin-left: 12px;
      }
    }

    .filter {
      position: absolute;
      top: 18px;
      right: 18px;
      width: 100px;
      z-index: 10;
      // background: #00385e !important;
      // 下面修改对应下拉框的样式：
      :deep(.el-select__wrapper) {
        background: #00385e;
        box-shadow: 0 0 0 1px #42b4f4 inset;
        // color: aliceblue;
      }
      :deep(.el-select__placeholder) {
        color: #fff;
        font-size: 0.8rem;
      }
      :deep(.el-scrollbar) {
        background-color: #00385e;
        --el-scrollbar-hover-bg-color: red;
      }
      :deep(.el-select__popper) {
        :deep(.el-popper) {
          background: #00e5ff;
          box-shadow: #42b4f4;
        }
      }
    }
  }

  .chart {
    position: relative;
    width: 100%;
    height: 250px;
    margin-top: 10px;
  }
}
</style>
