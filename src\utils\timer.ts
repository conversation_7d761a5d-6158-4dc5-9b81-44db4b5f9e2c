import { ref, onUnmounted, type Ref } from 'vue'

/**
 * 定时器配置接口
 */
export interface TimerConfig {
  /** 定时器间隔时间（毫秒），默认 1000ms */
  interval?: number
  /** 是否立即执行一次，默认 true */
  immediate?: boolean
  /** 是否自动开始，默认 true */
  autoStart?: boolean
}

/**
 * 定时器返回值接口
 */
export interface TimerReturn {
  /** 是否正在运行 */
  isRunning: Ref<boolean>
  /** 开始定时器 */
  start: () => void
  /** 停止定时器 */
  stop: () => void
  /** 重启定时器 */
  restart: () => void
  /** 销毁定时器 */
  destroy: () => void
}

/**
 * 定时器 Composable 函数
 *
 * @param callback 定时器回调函数
 * @param config 定时器配置
 * @returns 定时器控制对象
 */
export function useTimer(
  callback: () => void | Promise<void>,
  config: TimerConfig = {}
): TimerReturn {
  const { interval = 1000, immediate = true, autoStart = true } = config

  const isRunning = ref(false)
  let timerId: number | null = null

  /**
   * 执行回调函数
   */
  const executeCallback = async () => {
    try {
      await callback()
    } catch (error) {
      console.error('Timer callback error:', error)
    }
  }

  /**
   * 开始定时器
   */
  const start = () => {
    if (isRunning.value) return

    isRunning.value = true

    // 如果需要立即执行
    if (immediate) {
      executeCallback()
    }

    // 设置定时器
    timerId = window.setInterval(executeCallback, interval)
  }

  /**
   * 停止定时器
   */
  const stop = () => {
    if (!isRunning.value) return

    isRunning.value = false
    if (timerId !== null) {
      clearInterval(timerId)
      timerId = null
    }
  }

  /**
   * 重启定时器
   */
  const restart = () => {
    stop()
    start()
  }

  /**
   * 销毁定时器
   */
  const destroy = () => {
    stop()
  }

  // 如果设置了自动开始，则立即启动
  if (autoStart) {
    start()
  }

  // 组件卸载时自动清理定时器
  onUnmounted(() => {
    destroy()
  })

  return {
    isRunning,
    start,
    stop,
    restart,
    destroy
  }
}

/**
 * 数据轮询 Composable 函数
 * 专门用于定期请求数据的场景
 *
 * @param fetchFunction 数据获取函数
 * @param config 定时器配置
 * @returns 定时器控制对象和数据状态
 */
export function useDataPolling<T = any>(fetchFunction: () => Promise<T>, config: TimerConfig = {}) {
  const loading = ref(false)
  const error = ref<Error | null>(null)
  const data = ref<T | null>(null)

  /**
   * 包装的数据获取函数
   */
  const wrappedFetchFunction = async () => {
    try {
      loading.value = true
      error.value = null
      const result = await fetchFunction()

      // 确保数据格式正确
      if (result !== null && result !== undefined) {
        data.value = result
      } else {
        data.value = null
      }
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(String(err))
      data.value = null
      console.error('Data polling error:', err)
    } finally {
      loading.value = false
    }
  }

  const timer = useTimer(wrappedFetchFunction, config)

  return {
    ...timer,
    loading,
    error,
    data
  }
}

/**
 * 基于类的定时器基类
 * 适用于需要继承的场景
 */
export abstract class TimerBase {
  protected timer: TimerReturn | null = null
  protected config: TimerConfig

  constructor(config: TimerConfig = {}) {
    this.config = {
      interval: 1000,
      immediate: true,
      autoStart: true,
      ...config
    }
  }

  /**
   * 抽象方法：子类必须实现的定时器回调
   */
  protected abstract onTimer(): void | Promise<void>

  /**
   * 初始化定时器
   */
  protected initTimer() {
    if (this.timer) {
      this.timer.destroy()
    }

    this.timer = useTimer(() => this.onTimer(), this.config)
  }

  /**
   * 开始定时器
   */
  public start() {
    if (!this.timer) {
      this.initTimer()
    }
    this.timer?.start()
  }

  /**
   * 停止定时器
   */
  public stop() {
    this.timer?.stop()
  }

  /**
   * 重启定时器
   */
  public restart() {
    this.timer?.restart()
  }

  /**
   * 销毁定时器
   */
  public destroy() {
    this.timer?.destroy()
    this.timer = null
  }

  /**
   * 获取定时器运行状态
   */
  public get isRunning() {
    return this.timer?.isRunning.value || false
  }
}
