<template>
  <div class="debug-polling-test">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>基类的定时器设置</span>
          <div>
            <el-switch
              v-model="debugMode"
              @change="userPolling.setDebugMode(debugMode)"
              active-text="调试模式"
              inactive-text="静默模式"
            />
            <el-tag :type="userPolling.isRunning ? 'success' : 'danger'" style="margin-left: 10px">
              {{ userPolling.isRunning ? '运行中' : '已停止' }}
            </el-tag>
          </div>
        </div>
      </template>

      <!-- 控制面板 -->
      <div class="controls">
        <el-button :type="userPolling.isRunning ? 'danger' : 'primary'" @click="togglePolling">
          {{ userPolling.isRunning ? '停止轮询' : '开始轮询' }}
        </el-button>

        <el-button @click="userPolling.refresh()"> 手动刷新 </el-button>

        <el-button @click="showStatus"> 显示状态 </el-button>

        <el-input-number
          v-model="interval"
          :min="1000"
          :max="10000"
          :step="1000"
          @change="updateInterval"
          style="width: 150px; margin-left: 10px"
        />
        <span style="margin-left: 5px">毫秒</span>
      </div>

      <!-- 状态信息 -->
      <div class="status-info" style="margin-top: 20px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card shadow="never">
              <template #header>实时状态</template>
              <div class="status-item">
                <span class="label">轮询状态:</span>
                <el-tag :type="userPolling.isRunning ? 'success' : 'info'">
                  {{ userPolling.isRunning ? '运行中' : '已停止' }}
                </el-tag>
              </div>
              <div class="status-item">
                <span class="label">加载状态:</span>
                <el-tag :type="userPolling.loading.value ? 'warning' : 'success'">
                  {{ userPolling.loading.value ? '加载中' : '空闲' }}
                </el-tag>
              </div>
              <div class="status-item">
                <span class="label">总记录数:</span>
                <span class="value">{{ userPolling.total.value }}</span>
              </div>
              <div class="status-item">
                <span class="label">当前数据条数:</span>
                <span class="value">{{ userPolling.data.value.length }}</span>
              </div>
              <div class="status-item">
                <span class="label">轮询间隔:</span>
                <span class="value">{{ interval }}ms</span>
              </div>
            </el-card>
          </el-col>

          <el-col :span="12">
            <el-card shadow="never">
              <template #header>查询参数</template>
              <div class="status-item">
                <span class="label">页码:</span>
                <span class="value">{{ userPolling.queryParams.pageNo }}</span>
              </div>
              <div class="status-item">
                <span class="label">每页条数:</span>
                <span class="value">{{ userPolling.queryParams.pageSize }}</span>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 错误信息 -->
      <div v-if="userPolling.error.value" class="error-section" style="margin-top: 20px">
        <el-alert
          :title="`错误: ${userPolling.error.value?.message}`"
          type="error"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>请检查浏览器控制台获取详细错误信息</p>
          </template>
        </el-alert>
      </div>

      <!-- 数据预览 -->
      <div class="data-section" style="margin-top: 20px">
        <el-card shadow="never">
          <template #header>
            <div style="display: flex; justify-content: space-between; align-items: center">
              <span>数据预览 (前5条)</span>
              <el-tag>{{ userPolling.data.value.length }} 条数据</el-tag>
            </div>
          </template>

          <el-table
            :data="userPolling.data.value.slice(0, 5)"
            :loading="userPolling.loading.value"
            size="small"
            border
            empty-text="暂无数据"
          >
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="username" label="用户名" width="120" />
            <el-table-column prop="nickname" label="昵称" width="120" />
            <el-table-column prop="email" label="邮箱" />
            <el-table-column prop="status" label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="row.status === 0 ? 'success' : 'danger'" size="small">
                  {{ row.status === 0 ? '正常' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onUnmounted } from 'vue'
import { DebugUserDataPolling } from '@/utils/DebugUserDataPolling'
import { ElMessage } from 'element-plus'

/** 调试版定时器测试组件 */
defineOptions({ name: 'DebugPollingTest' })

const interval = ref(3000) // 默认3秒
const debugMode = ref(true)

// 创建调试版用户数据轮询实例
const userPolling = new DebugUserDataPolling(
  {
    pageNo: 1,
    pageSize: 10
  },
  {
    interval: interval.value,
    immediate: true,
    autoStart: true,
    continueOnError: true
  }
)

/**
 * 切换轮询状态
 */
const togglePolling = () => {
  if (userPolling.isRunning) {
    userPolling.stop()
    ElMessage.info('轮询已停止')
  } else {
    userPolling.start()
    ElMessage.success('轮询已开始')
  }
}

/**
 * 更新轮询间隔
 */
const updateInterval = (newInterval: number) => {
  userPolling.setInterval(newInterval)
  ElMessage.success(`轮询间隔已更新为 ${newInterval}ms`)
}

/**
 * 显示详细状态
 */
const showStatus = () => {
  const status = userPolling.getStatus()
  console.log('当前状态:', status)
  ElMessage.info('状态信息已输出到控制台')
}

// 组件卸载时清理资源
onUnmounted(() => {
  userPolling.destroy()
})
</script>

<style scoped>
.debug-polling-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.controls {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.status-item:last-child {
  border-bottom: none;
}

.label {
  font-weight: 500;
  color: #666;
}

.value {
  font-weight: bold;
  color: #333;
}
</style>
