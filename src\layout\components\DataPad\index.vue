<template>
  <div class="data-pad">
    <div class="Title">{{ title }}</div>
    <div class="nums">
      <span>{{ count }}</span>
      <span>{{ unit }}</span>
    </div>
  </div>
</template>

<script lang="js" setup>
defineProps({
  title: {
    type: String,
    default: "标题",
  },
  count: {
    type: String,
    default: "140.96",
  },
  unit: {
    type: String,
    default: "V",
  },
});
</script>

<style lang="less" scoped>
.data-pad {
  width: 205px;
  height: 94px;
  background-size: 100% 100%; //将背景图像的宽度和高度分别拉伸或压缩至与元素的宽度和高度一致。
  background-image: url("/src/assets/icons/padBG.png");
  .Title {
    /* Uab线电压 */
    padding-top: 13px;
    font-family: "OPPOSans";
    font-style: normal;
    font-weight: 400;
    font-size: 20px;
    line-height: 21px;
    text-align: center;
    letter-spacing: 1px;
    color: #ffffff;
  }
  .nums {
    /* 140.96V */
    font-family: "OPPOSans";
    font-style: normal;
    text-align: center;
    background: linear-gradient(180deg, #ffffff 26.32%, #5ec4fb 76.32%);
    -webkit-background-clip: text;
    font-weight: 600;
    -webkit-text-fill-color: transparent;
    span:nth-child(1) {
      font-size: 32px;
    }
    span:nth-child(2) {
      margin-left: 5px;
      font-size: 16px;
      line-height: 21px;
    }
  }
}
</style>
