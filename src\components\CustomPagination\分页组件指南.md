# CustomPagination 自定义分页组件

## 概述

这是一个自定义的分页组件，提供了与原始分页逻辑相同的功能，包括页码跳转、上一页/下一页按钮等。

## 功能特性

- ✅ 显示当前页、总页数、总条数信息
- ✅ 上一页/下一页按钮（带禁用状态）
- ✅ 页码跳转输入框
- ✅ 支持回车键快速跳转
- ✅ 完整的 TypeScript 类型支持
- ✅ 响应式设计，适配不同屏幕

## 使用方法

### 基础用法

```vue
<template>
  <div>
    <!-- 数据表格 -->
    <el-table :data="list" v-loading="loading">
      <!-- 表格列定义 -->
    </el-table>

    <!-- 自定义分页 -->
    <CustomPagination
      :current-page="queryParams.pageNo"
      :page-size="queryParams.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import CustomPagination from '@/components/CustomPagination/index.vue'

// 数据状态
const loading = ref(false)
const total = ref(0)
const list = ref([])

// 查询参数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10
  // 其他查询条件...
})

// 获取数据
const getList = async () => {
  loading.value = true
  try {
    const { data } = await yourApi.getPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

// 处理页码变化
const handleCurrentChange = (page: number) => {
  queryParams.pageNo = page
  getList()
}

// 初始化
onMounted(() => {
  getList()
})
</script>
```

## API 文档

### Props

| 参数           | 类型     | 默认值 | 说明     |
| -------------- | -------- | ------ | -------- |
| `current-page` | `number` | `1`    | 当前页码 |
| `page-size`    | `number` | `10`   | 每页条数 |
| `total`        | `number` | `0`    | 总条数   |

### Events

| 事件名           | 参数             | 说明                       |
| ---------------- | ---------------- | -------------------------- |
| `current-change` | `(page: number)` | 页码改变时触发             |
| `size-change`    | `(size: number)` | 每页条数改变时触发（预留） |

## 注意事项

1. **页码验证**: 组件会自动验证跳转页码的有效性
2. **边界处理**: 在第一页时"上一页"按钮会被禁用，在最后一页时"下一页"按钮会被禁用
3. **回车支持**: 在跳转输入框中按回车键可以快速跳转
4. **响应式**: 组件在不同屏幕尺寸下都能正常显示
