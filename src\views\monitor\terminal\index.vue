<template>
  <!-- 这里是终端监控 -->
  <header>
    <TopBar imgSrc="total_te_count.png" title="控制终端总数量" :count="12" :unit="个" />
    <TopBar imgSrc="used_te_count.png" title="在用终端数量" count="5" unit="个" />
    <TopBar imgSrc="free_te_count.png" title="空闲终端数量" count="5" unit="个" />
    <TopBar imgSrc="offline_te_count.png" title="离线终端数量" count="5" unit="个" />
  </header>
  <div class="mainCon">
    <div class="leftContent">
      <!-- UI设计图中未显示具体放置的物品，目前为单一循环 -->
      <div class="item" v-for="item of 3" :key="item">
        <div class="titles">XX港口-XX码头</div>
      </div>
    </div>
    <div class="rightContent">
      <pieChart icons="tools" :chartData="chartData" :isSimple="true">
        <template #title> 终端情况 </template>
      </pieChart>
      <!-- 这个是进度条，等待数据请求完毕使用v-for的形式 -->
      <div class="box">
        <div class="content">
          <span>在用</span>
          <span>70%</span>
        </div>
        <el-progress
          :text-inside="true"
          :stroke-width="14"
          :percentage="70"
          :stroke-linecap="square"
          :show-text="false"
        />
      </div>

      <div class="box">
        <div class="content">
          <span>空闲</span>
          <span>30%</span>
        </div>
        <el-progress
          :text-inside="true"
          :stroke-width="14"
          :percentage="70"
          :stroke-linecap="square"
          :show-text="false"
          status="success"
        />
      </div>

      <div class="box">
        <div class="content">
          <span>离线</span>
          <span>30%</span>
        </div>
        <el-progress
          :text-inside="true"
          :stroke-width="14"
          :percentage="70"
          :stroke-linecap="square"
          :show-text="false"
          status="warning"
        />
      </div>
    </div>
  </div>
  <!-- <div style="color: aliceblue">123</div> -->
  <!--  135 16  -->
</template>
<script setup lang="js">
import TopBar from '@/layout/components/TopBar/index.vue'
import pieChart from '@/layout/components/PieChart/index.vue'
//电压折线图的假数据
let chartData = ref([
  { value: 1, name: 'Active', color: '#00E5FF', label: '常规舰艇数' },
  { value: 1, name: 'Idle', color: '#FFD700', label: '外港舰艇数' },
  { value: 1, name: 'Offline', color: '#00FF97', label: '非供舰艇数' }
])
</script>

<style scoped lang="less">
// 引入公共样式
@import '@/assets/styles/common.less';

// 使用深度选择器修改 TopBar 组件内 .topBar 类的样式，不修改其他引入该组件的样式结构
::v-deep(.topBar) {
  width: calc(100% / 4);
}
::v-deep(.topBar .Title) {
  left: 130px;
}
::v-deep(.topBar .count) {
  right: 16px;
}

header {
  display: flex;
  justify-content: space-between;
}

.mainCon {
  width: 100%;
  height: calc(90vh - 120px);
  display: flex;
  .leftContent {
    height: 100%;
    width: 75%;
    .item {
      position: relative;
      background: rgba(0, 26, 44, 0.7);
      margin: 5px;
      height: 33%;
    }
  }
  .rightContent {
    position: relative;
    margin-top: 5px;
    background: rgba(0, 26, 44, 0.7);
    width: 25%;
    .box {
      height: 53px;
      padding: 5px;
      .content {
        display: flex;
        justify-content: space-between;
        margin: 3px 0;
      }
      border: 1px solid rgba(17, 97, 170, 1);
      margin: 10px 30px;
      span {
        color: white;
        font-family: OPPOSans;
        font-weight: 400;
        font-style: Medium;
        font-size: 14px;
        leading-trim: NONE;
        line-height: 18px;
        letter-spacing: 0%;
      }
    }
  }
}
</style>
