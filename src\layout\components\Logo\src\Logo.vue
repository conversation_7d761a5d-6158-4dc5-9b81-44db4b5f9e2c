<script lang="ts" setup>
import { computed, onMounted, ref, unref, watch } from 'vue'
import { useAppStore } from '@/store/modules/app'
import { useDesign } from '@/hooks/web/useDesign'

defineOptions({ name: 'Logo' })

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('logo')

const appStore = useAppStore()

const show = ref(true)

const title = computed(() => appStore.getTitle)

const layout = computed(() => appStore.getLayout)

const collapse = computed(() => appStore.getCollapse)

onMounted(() => {
  if (unref(collapse)) show.value = false
})

watch(
  () => collapse.value,
  (collapse: boolean) => {
    if (unref(layout) === 'topLeft' || unref(layout) === 'cutMenu') {
      show.value = true
      return
    }
    if (!collapse) {
      setTimeout(() => {
        show.value = !collapse
      }, 400)
    } else {
      show.value = !collapse
    }
  }
)

watch(
  () => layout.value,
  (layout) => {
    if (layout === 'top' || layout === 'cutMenu') {
      show.value = true
    } else {
      if (unref(collapse)) {
        show.value = false
      } else {
        show.value = true
      }
    }
  }
)
</script>

<template>
  <!-- 这里我们修改了相应的高度，高度改为70px -->
  <div class="height">
    <router-link
:class="[
      prefixCls,
      layout !== 'classic' ? `${prefixCls}__Top` : '',
      'flex !h-[var(--logo-height)] items-center cursor-pointer pl-8px  decoration-none'
    ]" to="/">
      <!-- <img class="h-[calc(var(--logo-height)-10px)] w-[calc(var(--logo-height)-10px)]" src="@/assets/imgs/logo.png" /> -->
      <transition name="fade">
        <div v-if="show" class='title'>
          {{ title }}
        </div>
      </transition>

    </router-link>
  </div>
</template>
<style>
.height {
  height: 70px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.title {
  /* 确保没有影响显示的样式 */
  /* 港口水电计量系统 */
  position: absolute;
  width: 368px;
  height: 52px;
  left: 48px;
  top: 8px;
  /*字体库中缺少这个字体，请注意 */
  font-family: 'YouSheBiaoTiHei';
  font-weight: 800;
  font-style: italic;
  /* 缺少特定字体结束 */
  font-size: 40px;
  line-height: 52px;
  /* identical to box height */
  text-align: center;
  letter-spacing: 4.6px;
  background: linear-gradient(180deg, #FFFFFF 22.81%, #82E1FF 77.19%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  z-index: 999;
  pointer-events: none;
}

/* 添加显隐动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
