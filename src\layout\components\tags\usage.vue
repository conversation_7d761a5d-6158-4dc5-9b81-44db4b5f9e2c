<template>
  <!-- 这个是记载用的电量和用水量的组件 -->
  <div class="usage" :class="props.types == 0 ? 'colorWater' : 'colorElectric'">
    <!-- 这么带入对应的图片 -->
    <img :src="`/src/assets/icons/${props.types}_${props.times}.png`" alt="" />
    <div class="usedCount">
      <span> {{ props.useCount }}</span><span>{{ props.types == 1 ? 'kwh' : 'm³' }}</span><br />
      <span>本{{ times == 'Y' ? '年' : times == 'M' ? '月' : '日' }}用{{ props.types == 0 ? '水' : '电' }}总量</span>
    </div>
  </div>
</template>
<script setup>
import { ref, reactive, defineProps } from 'vue'
//  组件接收参数：类型type:number水0 电1  时间单位times:string年Y/月M/日D  用量useCount:number 
const props = defineProps({
  useCount: {
    type: String,
    default: '0',
    required: true
  },
  types: {
    type: Number,
    default: 0,
    required: true
  },
  times: {
    type: String,
    default: 'Y',
    required: true
  }
})
</script>
<style lang="less" scoped>
img {
  position: absolute;
  left: 16.9px;
  top: 14px;
  width: 43px;
  height: 56px;
}

.colorElectric {
  border-left: 6px solid #EEBA00;
  background: linear-gradient(129.7deg, rgba(255, 196, 81, 0.42) 0%, rgba(255, 212, 129, 0.3276) 22.41%, rgba(255, 129, 129, 0) 129.77%);
}

.colorWater {
  border-left: 6px solid #21F8EB;
  background: linear-gradient(129.7deg, rgba(81, 255, 255, 0.42) 0%, rgba(129, 232, 255, 0.3276) 22.41%, rgba(129, 240, 255, 0) 129.77%);
}

.usage {
  height: 84px;
  width: 236px;
  position: relative;
  margin-top: 8px;

  .usedCount {
    color: #FFFFFF;
    position: absolute;
    left: 83.91px;
    top: 12.21px;

    &>span:nth-child(1) {
      font-family: DIN Alternate; //没有这个字体
      font-weight: 600;
      font-size: 30px;
      line-height: 100%;
      letter-spacing: 1px;

    }

    &>span:nth-child(2) {
      font-family: Source Han Sans CN;
      padding-left: 2px;
      font-weight: 400;
      font-size: 12px;
      line-height: 100%;
      letter-spacing: 1px;
    }

    &>span:nth-child(4) {
      font-family: Source Han Sans CN; //没有这个字体
      font-weight: 600;
      font-size: 20px;
      line-height: 100%;
      letter-spacing: 2.5px;
      vertical-align: middle;

    }
  }

}
</style>
