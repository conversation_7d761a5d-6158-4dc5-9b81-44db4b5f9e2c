import { DataPollingBase, type DataPollingConfig } from './DataPollingBase'
import { getUserPage } from '@/api/system/user'

/**
 * 调试版本的用户数据轮询类
 * 包含详细的日志输出，用于诊断问题
 */
export class DebugUserDataPolling extends DataPollingBase<any, PageParam> {
  private debugMode: boolean = true

  constructor(
    initialParams: Partial<PageParam> = {},
    config: DataPollingConfig = {}
  ) {
    super(initialParams, {
      interval: 2000, // 默认2秒，避免过于频繁
      immediate: true,
      autoStart: true,
      continueOnError: true,
      ...config
    })

    this.log('构造函数调用', { initialParams, config })
    
    // 自动初始化轮询
    this.initPolling()
  }

  private log(message: string, data?: any) {
    if (this.debugMode) {
      console.log(`[DebugUserDataPolling] ${message}`, data || '')
    }
  }

  /**
   * 实现抽象方法：调用用户列表API
   */
  protected async fetchData(params: PageParam): Promise<PageResult<any>> {
    this.log('开始获取数据', params)
    
    try {
      // 调用API
      const result = await getUserPage(params)
      
      this.log('API调用成功，原始返回数据:', result)
      
      // 验证返回数据格式
      if (!result) {
        this.log('错误: API返回null或undefined')
        throw new Error('API返回数据为空')
      }
      
      if (typeof result !== 'object') {
        this.log('错误: API返回数据不是对象', { type: typeof result, value: result })
        throw new Error('API返回数据格式不正确')
      }

      // 检查数据结构
      const hasListProperty = 'list' in result
      const hasTotalProperty = 'total' in result
      
      this.log('数据结构检查', {
        hasListProperty,
        hasTotalProperty,
        listType: hasListProperty ? typeof result.list : 'undefined',
        listIsArray: hasListProperty ? Array.isArray(result.list) : false,
        totalType: hasTotalProperty ? typeof result.total : 'undefined'
      })

      // 确保返回的数据有正确的结构
      const processedResult = {
        list: Array.isArray(result.list) ? result.list : [],
        total: typeof result.total === 'number' ? result.total : 0,
        ...result
      }
      
      this.log('处理后的数据:', processedResult)
      
      return processedResult
      
    } catch (error) {
      this.log('API调用失败:', error)
      throw error
    }
  }

  /**
   * 重写数据更新回调
   */
  protected onDataUpdated(result: PageResult<any>): void {
    this.log('数据更新回调', {
      total: result.total,
      listLength: result.list?.length || 0,
      firstItem: result.list?.[0] || null
    })
  }

  /**
   * 重写错误处理回调
   */
  protected onError(error: Error): void {
    this.log('错误处理回调', {
      message: error.message,
      stack: error.stack
    })
  }

  /**
   * 开启/关闭调试模式
   */
  public setDebugMode(enabled: boolean): void {
    this.debugMode = enabled
    this.log(`调试模式${enabled ? '开启' : '关闭'}`)
  }

  /**
   * 获取当前状态信息
   */
  public getStatus() {
    return {
      isRunning: this.isRunning,
      dataLength: this.data.value.length,
      total: this.total.value,
      loading: this.loading.value,
      error: this.error.value?.message || null,
      queryParams: this.queryParams
    }
  }
}

/**
 * 创建调试版用户数据轮询实例的工厂函数
 */
export function createDebugUserDataPolling(
  initialParams: Partial<PageParam> = {},
  config: DataPollingConfig = {}
): DebugUserDataPolling {
  return new DebugUserDataPolling(initialParams, config)
}
