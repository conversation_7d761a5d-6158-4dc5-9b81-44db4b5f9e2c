import request from '@/config/axios'

// 港口类型 VO
export interface TypeVO {
  portTypeSN: number // id
  protTypeName: string // 港口类型名称
}

// 港口类型 API
export const TypeApi = {
  // 查询港口类型分页
  getTypePage: async (params: any) => {
    return await request.get({ url: `/port/type/page`, params })
  },

  // 查询港口类型详情
  getType: async (id: number) => {
    return await request.get({ url: `/port/type/get?id=` + id })
  },

  // 新增港口类型
  createType: async (data: TypeVO) => {
    return await request.post({ url: `/port/type/create`, data })
  },

  // 修改港口类型
  updateType: async (data: TypeVO) => {
    return await request.put({ url: `/port/type/update`, data })
  },

  // 删除港口类型
  deleteType: async (id: number) => {
    return await request.delete({ url: `/port/type/delete?id=` + id })
  },

  // 导出港口类型 Excel
  exportType: async (params) => {
    return await request.download({ url: `/port/type/export-excel`, params })
  },
}
