<template>
  <div class="topBar">
    <TopBar imgSrc="total_wt_count.png" title="水箱总数量" :count="12" :unit="个" />
    <TopBar imgSrc="used_wt_count.png" title="在用水箱数量" count="10" unit="个" />
    <TopBar imgSrc="free_wt_count.png" title="空闲水箱数量" count="5" unit="个" />
  </div>
  <div class="mainContent">
    <aside>
      <SearchBox />
    </aside>
    <div class="right">
      <div class="rightTop">
        <!-- 电箱编号、实时数据 -->
        <ItemCard>
          <template #title> 12323456(电箱编号) </template>
        </ItemCard>

        <!-- 实时数据 -->
        <div class="currentData">
          <div class="titles">实时数据</div>
          <div class="dataPad">
            <DataPad title="控制水量" count="123.5" unit="吨" v-for="item in 3" :key="item" />
          </div>
        </div>
      </div>
      <div class="rightMed" style="color: white">
        <LineChart>
          <template #title> 这里是待定</template>
        </LineChart>
      </div>
      <div class="rightBottom">
        <!-- 电压折线图 -->
        <LineChart>
          <template #title> 电流曲线 </template>
        </LineChart>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import TopBar from '@/layout/components/TopBar/index.vue'
import SearchBox from '@/layout/components/searchBox/index.vue'
import DataPad from '@/layout/components/DataPad/index.vue'
import LineChart from '@/layout/components/LineChart/index.vue'
import ItemCard from '@/layout/components/itemCard/index.vue'
</script>

<style scoped lang="less">
// 引入公共样式
@import '@/assets/styles/common.less';
.topBar {
  display: flex;
  justify-content: space-between;
}

.mainContent {
  display: flex;
  height: calc(90vh - 120px);
  .right {
    // 减去搜索框的固定宽度
    width: calc(100% - 310px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .rightTop {
      display: flex;
      // justify-content: space-between;
      gap: 10px;
      .currentData {
        position: relative;
        margin: 0 auto;
        width: 80%;
        padding-top: 40px;
        padding-left: 10px;
        // 设置背景颜色
        background: rgba(0, 26, 44, 0.7);
        backdrop-filter: blur(6px);
        .dataPad {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 10px;
        }
      }
    }
  }
}
</style>
