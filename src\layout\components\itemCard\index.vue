<template>
  <div class="itemCard">
    <img src="/src/assets/icons/portIcon.png" alt="" />
    <div class="titles"><slot name="title"></slot></div>
    <div class="dataList">
      <div v-for="item in mockData" :key="item.title">
        <span class="data-title">{{ item.title }}</span
        >：
        <span class="data-value">{{ item.data }}</span>
      </div>
    </div>
  </div>
</template>
<script setup lang="js">
import { ref, reactive } from 'vue'
let mockData = ref([
  { title: '今日电量', data: '0KWh' },
  { title: '今日次数', data: '0次' },
  { title: '今日时长', data: '0小时' },
  { title: '累计电量', data: '475.13KWh' },
  { title: '累计次数', data: '10次' },
  { title: '累计时长', data: '0小时' },
  { title: '使用J艇代码', data: 'XXXX' },
  { title: '位置', data: 'XXX' }
])
</script>
<style lang="less" scoped>
// 引入公共样式
@import '@/assets/styles/common.less';
.itemCard {
  position: relative;
  width: max(500px, 18%);
  height: 240px;
  img {
    position: absolute;
    bottom: 5px;
    right: 5px;
  }
  .dataList {
    // 为了隔离上方的title
    padding-top: 42px;
    padding-left: 30px;
    font-family: OPPOSans;
    font-weight: 350;
    font-style: Medium;
    font-size: 13px;
    leading-trim: NONE;
    height: 23px;
    line-height: 23px;
    letter-spacing: 0px;
    color: rgba(255, 255, 255, 1);
  }
}
</style>
