// 统一配置字体样式
.text {
  font-family: Microsoft YaHei;
  letter-spacing: 1.2px;
}

// 标题样式
.titles {
  position: absolute;
  color: #fff;
  // width: 130px;
  height: 24px;
  top: 11px;
  left: 18px;
  .text();
  line-height: 24px;
  font-weight: 700;
  font-size: 18px;
  padding-left: 12px;
  z-index: 10;
}

.titles:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  background: linear-gradient(180deg, #00fff0 0%, #30a8ff 97.3%);
  width: 5px;
  height: inherit;
  display: block;
}
