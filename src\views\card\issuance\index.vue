<script setup lang="ts">
import ReadCardTop from '@/views/card/readInfo/readCardTop.vue'
import MyProcess from "@/views/card/issuance/component/myProcess.vue";

const selectValue = ref('1')

const titleData = [
  {
    prop: 'JTId',
    label: 'JT代码'
  },
  {
    prop: 'JT',
    label: 'JT舷号'
  },
  {
    prop: 'JTType',
    label: 'JT型号'
  },
  {
    prop: 'JTSubCode',
    label: 'JT隶属单位代码'
  },
  {
    prop: 'JTSubName',
    label: 'JT隶属单位名称'
  },
  {
    prop: 'JTGuaranteeCode',
    label: 'JT保障单位代码'
  },
  {
    prop: 'JTGuaranteeName',
    label: 'JT保障单位名称'
  },
  {
    prop: 'JTMotherPortCode',
    label: 'JT母港代码'
  },
  {
    prop: 'JTMotherPortName',
    label: 'JT母港名称'
  }
]

const selectDate = ref('')
</script>

<template>
  <div class="issuance-contant">

    <div class="top">
      <ReadCardTop />
      <div class="top-card">
        <div style="display: flex">
          <span>常规卡选择</span>
          <el-select label="制卡方式" v-model="selectValue" class="lable-select">
            <el-option label="常规JT卡" value="1" />
            <el-option label="卡2" value="2" />
          </el-select>
        </div>
        <div style="display: flex">
          <span>本港港点代码</span>
          <el-input class="lable-input" />
        </div>

        <div style="display: flex">
          <span>本港港点名称</span>
          <el-input class="lable-input" />
        </div>
        <div style="display: flex">
          <span>操作用户Id</span>
          <el-input class="lable-input" />
        </div>
      </div>
    </div>

    <div class="issuance-center">
      <div class="JTInfoTitle">
        <div class="line"></div>
        <span style="margin-left: 10px">卡片J艇信息配置</span>
      </div>
      <div style="margin: 0 30px">
        <el-form label-width="auto">
          <el-row :gutter="20">
            <el-col
              :span="8"
              style="margin-top: 30px"
              v-for="(item, index) in titleData"
              :key="index"
            >
              <el-form-item :label="item.label">
                <el-input style="height: 31px; width: 300px" />
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="本港使用有效期">
                <el-date-picker type="date" v-model="selectDate" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <MyProcess percentage="20" />
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <div class="issuance-center">
      <div class="JTInfoTitle">
        <div class="line"></div>
        <span style="margin-left: 10px">控制终端配置卡</span>
      </div>
      <div style="margin: 0 30px">
        <el-form label-width="auto">
          <el-row :gutter="20">
            <el-col
              :span="8"
              style="margin-top: 30px"
              v-for="(item, index) in titleData"
              :key="index"
            >
              <el-form-item :label="item.label">
                <el-input style="height: 31px; width: 300px" />
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="本港使用有效期">
                <el-date-picker type="date" v-model="selectDate" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <MyProcess percentage="20" />
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <div class="issuance-center">
      <div class="JTInfoTitle">
        <div class="line"></div>
        <span style="margin-left: 10px">管理员抄表卡</span>
      </div>

      <div style="margin: 20px 30px">
        <el-form label-width="auto" >
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="本港使用有效期">
                <el-date-picker type="date" v-model="selectDate" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <MyProcess percentage="20" />
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

  </div>
</template>

<style scoped lang="scss">
.issuance-contant {
  margin-top: 20px;
  color: #ffffff;
}

.top {
  padding: 10px 30px;
  background: rgba(0, 26, 44, 0.7);
}

.top-card {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  margin-top: 20px;
  align-items: center;
}

.lable-select {
  width: 158px;
  height: 31px;
  margin-left: 10px;
}

.lable-input {
  width: 158px;
  height: 31px;
  margin-left: 10px;
}

.issuance-center {
  margin-top: 20px;
  padding-bottom: 20px;
  background: rgba(0, 26, 44, 0.7);
}

.line {
  width: 5px;
  height: 17px;
  background: linear-gradient(180deg, #00fff0 0%, #30a8ff 97.3%);
  box-shadow: 2px 2px 4px 0px rgba(0, 44, 57, 0.5421);
}

.JTInfoTitle {
  display: flex;
  align-items: center;
  margin-left: 20px;
  padding-top: 20px;
}

</style>
