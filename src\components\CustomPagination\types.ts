// /**
//  * 这里只是类型说明，没有实际作用
//  */

/** 分页组件 Props */
export interface CustomPaginationProps {
  /** 当前页码 */
  currentPage: number
  /** 每页条数 */
  pageSize: number
  /** 总条数 */
  total: number
}

/** 分页组件 Emits */
export interface CustomPaginationEmits {
  'current-change': (page: number) => void
  'size-change': (size: number) => void
}

/** 分页组件实例类型 */
export interface CustomPaginationInstance {
  goToPage: (page: number) => void
  reset: () => void
}
