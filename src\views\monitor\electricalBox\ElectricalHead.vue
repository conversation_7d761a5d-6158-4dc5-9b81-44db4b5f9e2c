<template>
  <div class="electric-box-card">
    <el-row :gutter="50" align="middle" justify="center">
      <el-col :span="8">
        <!-- 左侧图标区域 -->
        <div class="icon-container">
          <img  :src="getImg('electricalBox.png')"  alt=""/>
        </div>
      </el-col>
      <el-col :span="8">
        <!-- 中间文字 -->
        <div class="text-container">
          <div>电箱总数量</div>
        </div>
      </el-col>

      <el-col :span="8">
        <!-- 右边数字 -->
        <div class="num-container">
          <div>15</div>
          <div>个</div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
const getImg = (imgName) => {
  return new URL(`/src/assets/imgs/monitory/${imgName}`, import.meta.url).href
}
</script>

<style scoped>
.electric-box-card {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(180deg, rgba(44, 155, 235, 0.45) 0%, rgba(0, 33, 96, 0.29) 100%);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-image-source: linear-gradient(203.23deg, #ffffff -1.64%, rgba(30, 107, 153, 0) 53.74%);
  max-width: 400px;
  height: 120px;
}

.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 24px;
}

.text-container {
  display: flex;
}

.num-container {
  display: flex;
}
</style>
