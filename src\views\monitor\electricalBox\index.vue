<template>
  <div class="topBar">
    <TopBar imgSrc="total_el_count.png" title="电箱总数量" :count="12" :unit="个" />
    <TopBar imgSrc="used_el_count.png" title="在用电箱数量" count="10" unit="个" />
    <TopBar imgSrc="free_el_count.png" title="空闲电箱数量" count="5" unit="个" />
  </div>
  <div class="mainContent">
    <!-- 选择设备 -->
    <div class="selectBox">
      <SearchBox />
    </div>
    <!-- 右侧展示部分 -->
    <div class="rightBox">
      <div class="rightTop">
        <!-- 电箱编号、实时数据 -->
        <ItemCard>
          <template #title> 12323456(电箱编号) </template>
        </ItemCard>

        <!-- 实时数据 -->
        <div class="currentData">
          <div class="titles">实时数据</div>
          <div class="dataPad">
            <DataPad title="控制电压" count="123.5" unit="V" v-for="item in 8" :key="item" />
          </div>
        </div>
      </div>
      <!-- 三相电有效值 -->
      <div class="rightBottom">
        <BarChart title="电压有效值" :data="serverData" v-for="item in 6" :key="item" />
      </div>
    </div>
  </div>
</template>

<script setup lang="js">
import { ref } from 'vue'
import SearchBox from '@/layout/components/searchBox/index.vue'
import BarChart from '@/layout/components/BarChart/index.vue'
import ItemCard from '@/layout/components/itemCard/index.vue'
import TopBar from '@/layout/components/TopBar/index.vue'
import DataPad from '@/layout/components/DataPad/index.vue'

const serverData = ref([
  { property: 'A相', value: '120.0' },
  { property: 'B相', value: '80.0' },
  { property: 'C相', value: '220.0' }
])
</script>

<style scoped lang="less">
// 引入公共样式
@import '@/assets/styles/common.less';

.topBar {
  display: flex;
  justify-content: space-between;
  // margin: 20px 0;
}
.mainContent {
  display: flex;
  height: 75vh;
  gap: 10px;
  .rightBox {
    width: 100%;
    .rightTop {
      display: flex;
      // justify-content: space-between;
      gap: 10px;
      .currentData {
        position: relative;
        margin: 0 auto;
        width: 80%;
        padding-top: 40px;
        padding-left: 10px;
        // 设置背景颜色
        background: rgba(0, 26, 44, 0.7);
        backdrop-filter: blur(6px);
        .dataPad {
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          gap: 10px;
        }
      }
    }
    .rightBottom {
      display: grid;
      // 两行三列
      grid-template-columns: 1fr 1fr 1fr;
      grid-template-rows: 1fr 1fr;
      gap: 10px;
    }
  }
}
</style>
