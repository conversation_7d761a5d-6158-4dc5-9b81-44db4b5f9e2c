<script setup lang="ts">
import { titleDataType } from '../type'

const props = defineProps({
  titleData: {
    type: Array as PropType<titleDataType>,
    default: () => []
  },
  title: {
    type: String,
    default: '卡片信息'
  }
})
</script>

<template>
  <div class="c-contant">
    <div class="contant-title">{{ title }}</div>
    <div class="card-info">
      <el-form>
        <el-row :gutter="20">
          <el-col :span="12" v-for="(item, index) in titleData" :key="index">
            <el-form-item :label="item.label" :prop="item.prop">
              <el-input style="width: 315px" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<style scoped lang="scss">
.c-contant {
  width: 800px;
  color: #ffffff;
  background-color: #00204a;
  padding: 20px;
  border: 1px solid #006eff;
}

.contant-title {
  position: relative;
  left: 20px;
  top: -30px;
  width: 70px;
  height: 20px;
  text-align: center;
  background: #0f234b;
  z-index: 0;
}

.card-info {
}
</style>
